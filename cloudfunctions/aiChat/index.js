// cloudfunctions/aiChat/index.js
const cloud = require('wx-server-sdk')

cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

/**
 * AI对话处理云函数
 * 集成DeepSeek模型，处理用户消息并提取简历信息
 */
exports.main = async (event, context) => {
  const { message, conversationId, currentStage, resumeData } = event
  const { OPENID } = cloud.getWXContext()

  try {
    // 调用DeepSeek API处理消息
    const aiResponse = await processWithDeepSeek(message, currentStage, resumeData)
    
    // 提取结构化信息
    const extractedData = await extractStructuredInfo(message, aiResponse, currentStage)
    
    // 确定下一个对话阶段
    const nextStage = determineNextStage(currentStage, extractedData)
    
    // 生成建议回复
    const suggestions = generateSuggestions(currentStage, nextStage)
    
    // 更新对话记录
    await updateConversationRecord(conversationId, OPENID, message, aiResponse, extractedData)

    return {
      success: true,
      aiResponse: aiResponse,
      extractedData: extractedData,
      nextStage: nextStage,
      suggestions: suggestions
    }
  } catch (error) {
    console.error('AI对话处理失败:', error)
    return {
      success: false,
      error: error.message,
      // 提供降级回复
      aiResponse: getFallbackResponse(currentStage),
      extractedData: null,
      nextStage: currentStage,
      suggestions: []
    }
  }
}

/**
 * 调用DeepSeek API处理消息
 * TODO: 集成真实的DeepSeek API
 */
async function processWithDeepSeek(message, stage, resumeData) {
  // 暂时返回模拟回复，后续替换为真实API调用
  const mockResponses = {
    greeting: '很高兴认识你！我是小简，你的专属简历顾问。先告诉我你想找什么样的工作吧？',
    intention: '好的，我了解你的求职意向了。现在告诉我你的基本信息吧，比如姓名、联系方式等。',
    basicInfo: '谢谢你的基本信息！接下来聊聊你的教育背景吧。',
    education: '你的教育背景很不错！现在说说你的工作经验吧。',
    experience: '你的工作经历很有价值！最后告诉我你掌握了哪些技能？',
    skills: '太棒了！现在我已经收集了足够的信息来为你制作简历。',
    completion: '🎉 恭喜！你的简历信息已经收集完成。'
  }
  
  return mockResponses[stage] || mockResponses.greeting
}

/**
 * 从对话中提取结构化信息
 */
async function extractStructuredInfo(message, aiResponse, stage) {
  // 简单的信息提取逻辑，后续可以使用更复杂的NLP处理
  const extractedData = {}
  
  switch (stage) {
    case 'intention':
      if (message.includes('工程师') || message.includes('开发')) {
        extractedData.jobIntention = { position: message.trim() }
      }
      break
    case 'basicInfo':
      // 提取姓名
      const nameMatch = message.match(/我叫(.+)|我是(.+)|姓名[：:](.+)/)
      if (nameMatch) {
        extractedData.personalInfo = { 
          name: (nameMatch[1] || nameMatch[2] || nameMatch[3]).trim() 
        }
      }
      break
    case 'education':
      if (message.includes('大学') || message.includes('学院')) {
        extractedData.education = [{
          school: '用户提到的学校',
          major: '相关专业',
          degree: '学历'
        }]
      }
      break
    case 'experience':
      if (message.includes('公司') || message.includes('工作')) {
        extractedData.workExperience = [{
          company: '用户提到的公司',
          position: '职位',
          responsibilities: ['工作职责']
        }]
      }
      break
    case 'skills':
      // 提取技能关键词
      const skills = []
      const skillKeywords = ['JavaScript', 'Python', 'Java', 'React', 'Vue', 'Node.js', 'Excel', 'PPT']
      skillKeywords.forEach(skill => {
        if (message.includes(skill)) {
          skills.push(skill)
        }
      })
      if (skills.length > 0) {
        extractedData.skills = skills
      }
      break
  }
  
  return extractedData
}

/**
 * 确定下一个对话阶段
 */
function determineNextStage(currentStage, extractedData) {
  const stageFlow = {
    greeting: 'intention',
    intention: 'basicInfo',
    basicInfo: 'education',
    education: 'experience',
    experience: 'skills',
    skills: 'completion',
    completion: 'completion'
  }
  
  return stageFlow[currentStage] || 'completion'
}

/**
 * 生成建议回复
 */
function generateSuggestions(currentStage, nextStage) {
  const suggestions = {
    greeting: ['前端开发工程师', '产品经理', '数据分析师'],
    intention: ['我叫张三', '我在北京', '我的手机是...'],
    basicInfo: ['本科毕业', '研究生学历', '专科学历'],
    education: ['刚毕业没经验', '有1-3年经验', '有3年以上经验'],
    experience: ['JavaScript', 'Python', 'Excel'],
    skills: ['预览简历', '继续完善', '选择模板'],
    completion: []
  }
  
  return suggestions[currentStage] || []
}

/**
 * 更新对话记录
 */
async function updateConversationRecord(conversationId, userId, userMessage, aiResponse, extractedData) {
  const db = cloud.database()
  
  // 这里应该更新对话记录，暂时省略具体实现
  console.log('更新对话记录:', { conversationId, userId, userMessage, aiResponse, extractedData })
}

/**
 * 获取降级回复
 */
function getFallbackResponse(stage) {
  return '抱歉，我现在有点忙，请稍后再试。或者你可以继续告诉我更多信息。'
}
