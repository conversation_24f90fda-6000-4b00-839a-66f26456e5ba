# 《简历故事》小程序开发计划文档

## 📋 项目概述

### 产品愿景
> "让每个人都能用最自然的方式，讲述属于自己的职业故事，获得心仪的工作机会。"

基于现有的微信小程序模板项目，我们将开发一个名为《简历故事》的AI驱动简历制作小程序，通过人性化的对话方式帮助用户挖掘职业亮点，生成个性化简历。

---

## 🎯 一、项目分析阶段

### 1.1 现有项目架构分析

**已有AI能力：**
- ✅ 腾讯云开发环境 (aicv-3g1rr6glfc42a1eb)
- ✅ agent-ui组件库（完整的对话UI组件）
- ✅ DeepSeek模型集成能力
- ✅ 云函数架构支持
- ✅ 文件上传和处理能力
- ✅ 多轮对话管理

**技术栈：**
- 前端：微信小程序原生框架
- 后端：腾讯云开发 + Node.js云函数
- AI：DeepSeek-V3模型
- 数据库：MongoDB云数据库
- 存储：腾讯云存储

### 1.2 产品需求分析

**核心功能需求：**
1. **故事化对话引擎** - 通过自然对话收集用户信息
2. **智能信息提取** - AI自动结构化用户经历
3. **职位匹配优化** - 基于JD定制简历内容
4. **简历生成导出** - 多模板、多格式输出
5. **进度可视化** - 实时显示简历完整度

---

## 🏗️ 二、技术方案设计

### 2.1 整体架构设计

```mermaid
graph TB
    A[微信小程序前端] --> B[腾讯云开发]
    B --> C[DeepSeek AI引擎]
    B --> D[云数据库]
    B --> E[云存储]
    B --> F[云函数]
    
    C --> G[对话理解模块]
    C --> H[简历优化模块]
    C --> I[职位匹配模块]
    
    F --> J[用户管理]
    F --> K[简历生成]
    F --> L[AI分析]
    F --> M[文件处理]
```

### 2.2 核心模块设计

#### 2.2.1 故事化对话引擎

**对话阶段设计：**
```javascript
const CONVERSATION_STAGES = {
  GREETING: '初次见面',
  INTENTION: '求职意向',
  BASIC_INFO: '基本信息',
  EDUCATION: '教育背景',
  EXPERIENCE: '工作经历',
  PROJECTS: '项目经验',
  SKILLS: '技能特长',
  COMPLETION: '完成总结'
}
```

#### 2.2.2 DeepSeek AI集成

**API配置：**
```javascript
const DEEPSEEK_CONFIG = {
  model: 'deepseek-v3',
  baseURL: 'https://api.deepseek.com/v1',
  temperature: 0.7,
  maxTokens: 2000,
  systemPrompt: `你是一位专业的简历顾问，具有以下特质：
    1. 温暖友善，像朋友一样交流
    2. 专业敏锐，能发现用户的亮点和价值
    3. 耐心引导，帮助用户深入挖掘经历
    4. 善于鼓励，让用户重新认识自己的价值`
}
```

### 2.3 数据结构设计

**用户简历数据模型：**
```javascript
const ResumeDataSchema = {
  userId: String,
  personalInfo: {
    name: String,
    phone: String,
    email: String,
    location: String
  },
  jobIntention: {
    position: String,
    industry: String,
    salary: String,
    location: String
  },
  education: [{
    school: String,
    major: String,
    degree: String,
    startDate: Date,
    endDate: Date,
    achievements: [String]
  }],
  workExperience: [{
    company: String,
    position: String,
    startDate: Date,
    endDate: Date,
    responsibilities: [String],
    achievements: [String]
  }],
  projects: [{
    name: String,
    role: String,
    description: String,
    technologies: [String],
    achievements: [String]
  }],
  skills: [String],
  personalTraits: [String],
  completeness: Number,
  createdAt: Date,
  updatedAt: Date
}
```

---

## 📱 三、前端开发计划

### 3.1 页面结构设计

```
pages/
├── index/              # 启动页
├── chat/               # 对话制作页
├── preview/            # 简历预览页
├── optimize/           # 职位优化页
├── templates/          # 模板选择页
├── export/             # 导出页面
└── profile/            # 个人中心
```

### 3.2 核心组件开发

**基于现有agent-ui组件扩展：**

1. **chat-bubble** - 对话气泡（已有，需定制）
2. **progress-ring** - 进度环组件
3. **resume-card** - 简历预览卡片
4. **skill-tag** - 技能标签
5. **template-selector** - 模板选择器

### 3.3 设计系统实现

**基于乔纳森·艾维的设计理念：**
```css
/* 温暖而专业的色彩系统 */
:root {
  --primary-500: #0ea5e9;    /* 信任蓝 */
  --success-500: #22c55e;    /* 成功绿 */
  --warning-500: #f59e0b;    /* 提醒橙 */
  --neutral-0: #ffffff;      /* 纯净白 */
  --neutral-900: #171717;    /* 深邃黑 */
}
```

---

## ⚙️ 四、后端开发计划

### 4.1 云函数架构

**需要开发的云函数：**

1. **aiChat** - AI对话处理
2. **resumeGenerate** - 简历生成
3. **jobAnalyze** - 职位分析
4. **templateManage** - 模板管理
5. **userManage** - 用户管理

### 4.2 核心云函数实现

#### 4.2.1 AI对话处理函数

```javascript
// cloudfunctions/aiChat/index.js
const cloud = require('wx-server-sdk')
const { Configuration, OpenAIApi } = require('openai')

cloud.init()

const deepseekConfig = new Configuration({
  apiKey: process.env.DEEPSEEK_API_KEY,
  basePath: 'https://api.deepseek.com/v1'
})

exports.main = async (event, context) => {
  const { message, conversationId, currentStage, resumeData } = event
  
  try {
    // 调用DeepSeek API
    const response = await processConversation(message, currentStage, resumeData)
    
    // 提取结构化信息
    const extractedInfo = await extractStructuredInfo(message, response)
    
    // 更新对话记录
    await updateConversation(conversationId, message, response, extractedInfo)
    
    return {
      aiResponse: response,
      extractedInfo: extractedInfo,
      nextStage: determineNextStage(currentStage, extractedInfo),
      suggestions: generateSuggestions(currentStage)
    }
  } catch (error) {
    console.error('AI对话处理失败:', error)
    return { error: error.message }
  }
}
```

#### 4.2.2 简历生成函数

```javascript
// cloudfunctions/resumeGenerate/index.js
exports.main = async (event, context) => {
  const { userId, templateId, jobDescription } = event
  
  try {
    // 获取用户简历数据
    const resumeData = await getUserResumeData(userId)
    
    // 如果有职位描述，进行优化
    if (jobDescription) {
      resumeData = await optimizeForJob(resumeData, jobDescription)
    }
    
    // 生成简历
    const resume = await generateResume(resumeData, templateId)
    
    // 保存到云存储
    const fileId = await saveResumeFile(resume, userId)
    
    return {
      success: true,
      fileId: fileId,
      downloadUrl: await getDownloadUrl(fileId)
    }
  } catch (error) {
    return { error: error.message }
  }
}
```

### 4.3 数据库设计

**集合结构：**
```javascript
// users - 用户信息
{
  _id: ObjectId,
  openid: String,
  nickname: String,
  avatar: String,
  createdAt: Date
}

// conversations - 对话记录
{
  _id: ObjectId,
  userId: String,
  stage: String,
  messages: [{
    role: String, // 'user' | 'assistant'
    content: String,
    timestamp: Date
  }],
  resumeData: Object,
  createdAt: Date,
  updatedAt: Date
}

// resumes - 简历记录
{
  _id: ObjectId,
  userId: String,
  data: Object, // 简历数据
  templateId: String,
  fileId: String,
  completeness: Number,
  createdAt: Date,
  updatedAt: Date
}
```

---

## 🎨 五、用户体验设计

### 5.1 对话流程设计

**基于乔布斯的极简主义：**

1. **欢迎阶段** - 温暖的开场，建立信任
2. **意向探索** - 了解求职目标
3. **信息收集** - 循序渐进收集经历
4. **价值发现** - 帮助用户认识自己的价值
5. **故事润色** - 优化表达方式
6. **完成确认** - 生成最终简历

### 5.2 交互设计原则

**乔纳森的人性化设计：**
- **直觉交互** - 用户无需学习，自然对话
- **情感连接** - AI有温度，像朋友一样
- **无缝体验** - 技术在背后，用户感受魔法

### 5.3 视觉设计规范

**设计令牌系统：**
```css
/* 字体系统 */
--font-size-xs: 24rpx;
--font-size-sm: 28rpx;
--font-size-base: 32rpx;
--font-size-lg: 36rpx;

/* 间距系统 */
--spacing-1: 8rpx;
--spacing-2: 16rpx;
--spacing-4: 32rpx;
--spacing-8: 64rpx;

/* 圆角系统 */
--radius-sm: 8rpx;
--radius-md: 16rpx;
--radius-lg: 24rpx;
--radius-full: 9999rpx;
```

---

## 📋 六、开发计划与里程碑

### 6.1 开发阶段划分

**第一阶段：基础框架搭建（1-2周）**
- [ ] 项目结构调整
- [ ] 基础页面创建
- [ ] agent-ui组件定制
- [ ] 云函数基础架构
- [ ] 数据库设计实现

**第二阶段：核心功能开发（2-3周）**
- [ ] AI对话引擎开发
- [ ] 信息提取模块
- [ ] 简历数据管理
- [ ] 进度可视化
- [ ] 基础简历生成

**第三阶段：高级功能开发（2-3周）**
- [ ] 职位匹配优化
- [ ] 多模板支持
- [ ] 简历导出功能
- [ ] 用户体验优化
- [ ] 性能优化

**第四阶段：测试与发布（1-2周）**
- [ ] 功能测试
- [ ] 用户体验测试
- [ ] 性能测试
- [ ] 小程序审核
- [ ] 正式发布

### 6.2 技术风险与解决方案

**风险识别：**
1. **DeepSeek API稳定性** - 实现降级策略
2. **信息提取准确性** - 多轮验证机制
3. **用户体验一致性** - 完善的测试流程
4. **小程序审核风险** - 合规性检查

**解决方案：**
- 实现API重试机制和降级策略
- 建立完善的错误处理和用户反馈机制
- 制定详细的测试用例和验收标准

---

## 🚀 七、实施准备

### 7.1 环境配置

**开发环境要求：**
- 微信开发者工具
- Node.js 16+
- 腾讯云开发环境
- DeepSeek API密钥

### 7.2 依赖管理

**需要安装的依赖：**
```json
{
  "dependencies": {
    "wx-server-sdk": "^2.6.3",
    "openai": "^4.0.0",
    "moment": "^2.29.4",
    "lodash": "^4.17.21"
  }
}
```

### 7.3 代码规范

**遵循现有项目规范：**
- ESLint配置
- 组件命名规范
- 文件结构规范
- 注释规范

---

## 📊 八、成功指标

### 8.1 产品指标
- 用户完成率 > 80%
- 简历生成成功率 > 95%
- 用户满意度 > 4.5/5.0
- 平均对话轮次 < 20轮

### 8.2 技术指标
- API响应时间 < 2秒
- 页面加载时间 < 1秒
- 错误率 < 1%
- 可用性 > 99.5%

---

## 🎯 九、后续规划

### 9.1 功能扩展
- 简历模板商店
- AI面试辅导
- 职业规划建议
- 求职进度跟踪

### 9.2 技术优化
- 离线能力支持
- 多语言支持
- 语音交互
- 图像识别

---

## 📈 十、进度跟踪

### 10.1 当前状态
- 项目分析：✅ 完成
- 需求理解：✅ 完成
- 技术方案设计：✅ 完成
- 开发计划制定：✅ 完成
- 实施准备：🔄 进行中

### 10.2 下一步行动
1. 开始第一阶段开发
2. 项目结构调整
3. 基础页面创建

---

*本文档将持续更新，记录开发进度和重要决策。*
