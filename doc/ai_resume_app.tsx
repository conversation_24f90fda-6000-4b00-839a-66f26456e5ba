import React, { useState, useEffect, useRef } from 'react';
import { MessageCircle, User, FileText, Send, Sparkles, Download, ArrowLeft, Check, ChevronRight } from 'lucide-react';

// 设计令牌作为CSS变量
const DesignTokens = () => (
  <style>{`
    :root {
      /* Colors */
      --color-primary-500: #0ea5e9;
      --color-primary-100: #e0f2fe;
      --color-primary-50: #f0f9ff;
      --color-primary-200: #bae6fd;
      --color-primary-700: #0369a1;
      
      --color-neutral-0: #ffffff;
      --color-neutral-50: #fafafa;
      --color-neutral-100: #f5f5f5;
      --color-neutral-200: #e5e5e5;
      --color-neutral-300: #d4d4d4;
      --color-neutral-400: #a3a3a3;
      --color-neutral-600: #525252;
      --color-neutral-700: #404040;
      --color-neutral-900: #171717;
      
      --color-success-base: #16a34a;
      --color-success-light: #dcfce7;
      
      /* Typography */
      --font-size-xs: 12px;
      --font-size-sm: 14px;
      --font-size-base: 16px;
      --font-size-lg: 18px;
      --font-size-xl: 20px;
      --font-size-2xl: 24px;
      
      --font-weight-normal: 400;
      --font-weight-medium: 500;
      --font-weight-semibold: 600;
      
      /* Spacing */
      --spacing-1: 4px;
      --spacing-2: 8px;
      --spacing-3: 12px;
      --spacing-4: 16px;
      --spacing-5: 20px;
      --spacing-6: 24px;
      --spacing-8: 32px;
      
      /* Border Radius */
      --border-radius-sm: 4px;
      --border-radius-md: 8px;
      --border-radius-lg: 12px;
      --border-radius-xl: 16px;
      --border-radius-2xl: 24px;
      --border-radius-full: 9999px;
      
      /* Shadows */
      --shadow-xs: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
      --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
      --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
      --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
      
      /* Animation */
      --duration-fast: 150ms;
      --duration-normal: 200ms;
      --duration-slow: 300ms;
      --easing-ease-out: cubic-bezier(0, 0, 0.2, 1);
    }
    
    * {
      box-sizing: border-box;
    }
    
    body {
      margin: 0;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      background: var(--color-neutral-50);
      color: var(--color-neutral-900);
    }
  `}</style>
);

// AI智能分析工具
const AIAnalyzer = {
  // 提取姓名和联系方式
  extractContactInfo: (text) => {
    const emailRegex = /[\w\.-]+@[\w\.-]+\.\w+/;
    const phoneRegex = /1[3-9]\d{9}/;
    const email = text.match(emailRegex)?.[0];
    const phone = text.match(phoneRegex)?.[0];
    
    // 简单的姓名提取（假设前两个中文字符是姓名）
    const nameMatch = text.match(/[\u4e00-\u9fa5]{2,4}/);
    const name = nameMatch?.[0];
    
    return { name, email, phone };
  },
  
  // 分析工作经历亮点
  analyzeExperience: (text) => {
    const achievements = [];
    
    // 检测数字+百分比
    const percentageMatches = text.match(/\d+%/g);
    if (percentageMatches) {
      achievements.push(...percentageMatches.map(p => `提升了${p}`));
    }
    
    // 检测金额
    const moneyMatches = text.match(/\d+万|\d+千/g);
    if (moneyMatches) {
      achievements.push(...moneyMatches.map(m => `创造了${m}价值`));
    }
    
    // 检测技能关键词
    const skillKeywords = ['Python', 'Java', 'React', 'Vue', '数据分析', '项目管理', '团队协作', 'SQL', 'Excel'];
    const detectedSkills = skillKeywords.filter(skill => 
      text.toLowerCase().includes(skill.toLowerCase()) || text.includes(skill)
    );
    
    return { achievements, skills: detectedSkills };
  },
  
  // 生成建议和改进点
  generateSuggestions: (resumeData) => {
    const suggestions = [];
    
    if (!resumeData.achievements || resumeData.achievements.length === 0) {
      suggestions.push('建议添加具体的数字成果，如"提升了30%的用户活跃度"');
    }
    
    if (!resumeData.skills || resumeData.skills.length < 3) {
      suggestions.push('可以补充更多技术技能，提升竞争力');
    }
    
    if (!resumeData.experience) {
      suggestions.push('项目经历很重要，建议详细描述1-2个核心项目');
    }
    
    return suggestions;
  }
};

// 简历优化器
const ResumeOptimizer = {
  // 根据岗位描述优化简历
  optimizeForJob: (resumeData, jobDescription) => {
    // 这里模拟JD分析和简历优化
    const jobKeywords = ['数据分析', '项目管理', 'Python', '团队协作'];
    const optimizedData = { ...resumeData };
    
    // 重新排序技能，突出匹配度高的
    if (optimizedData.skills) {
      optimizedData.skills = optimizedData.skills.sort((a, b) => {
        const aMatch = jobKeywords.some(keyword => a.includes(keyword));
        const bMatch = jobKeywords.some(keyword => b.includes(keyword));
        return bMatch - aMatch;
      });
    }
    
    return optimizedData;
  },
  
  // 计算简历完整度评分
  calculateScore: (resumeData) => {
    const requiredFields = ['name', 'contact', 'jobIntention', 'education', 'experience'];
    const optionalFields = ['skills', 'achievements'];
    
    let score = 0;
    
    // 必需字段权重70%
    requiredFields.forEach(field => {
      if (resumeData[field]) score += 14; // 70/5 = 14
    });
    
    // 可选字段权重30%
    optionalFields.forEach(field => {
      if (resumeData[field] && resumeData[field].length > 0) score += 15; // 30/2 = 15
    });
    
    return Math.min(score, 100);
  }
};

// 输入状态指示器组件
const TypingIndicator = () => (
  <div className="typing-indicator">
    <div className="typing-dot"></div>
    <div className="typing-dot"></div>
    <div className="typing-dot"></div>
    <style>{`
      .typing-indicator {
        display: flex;
        align-items: center;
        gap: var(--spacing-2);
        padding: var(--spacing-4) var(--spacing-5);
        background: var(--color-neutral-100);
        border-radius: var(--border-radius-2xl);
        max-width: 80px;
      }
      
      .typing-dot {
        width: 8px;
        height: 8px;
        border-radius: var(--border-radius-full);
        background: var(--color-neutral-400);
        animation: typing-pulse 1.4s ease-in-out infinite;
      }
      
      .typing-dot:nth-child(2) {
        animation-delay: 0.2s;
      }
      
      .typing-dot:nth-child(3) {
        animation-delay: 0.4s;
      }
      
      @keyframes typing-pulse {
        0%, 60%, 100% {
          opacity: 0.4;
          transform: scale(1);
        }
        30% {
          opacity: 1;
          transform: scale(1.2);
        }
      }
    `}</style>
  </div>
);

// 对话气泡组件
const ChatBubble = ({ message, isAI, isTyping }) => (
  <div className={`chat-message ${isAI ? 'ai' : 'user'}`}>
    {isAI && (
      <div className="avatar">
        <Sparkles size={16} />
      </div>
    )}
    <div className="message-content">
      {isTyping ? <TypingIndicator /> : message}
    </div>
    <style>{`
      .chat-message {
        display: flex;
        gap: var(--spacing-3);
        margin-bottom: var(--spacing-4);
        animation: slideUp var(--duration-normal) var(--easing-ease-out);
      }
      
      .chat-message.user {
        flex-direction: row-reverse;
        align-self: flex-end;
      }
      
      .avatar {
        width: 32px;
        height: 32px;
        border-radius: var(--border-radius-full);
        background: var(--color-primary-500);
        display: flex;
        align-items: center;
        justify-content: center;
        color: var(--color-neutral-0);
        flex-shrink: 0;
      }
      
      .message-content {
        max-width: 80%;
        padding: var(--spacing-4) var(--spacing-5);
        border-radius: var(--border-radius-2xl);
        font-size: var(--font-size-base);
        line-height: 1.5;
        box-shadow: var(--shadow-xs);
      }
      
      .chat-message.ai .message-content {
        background: var(--color-neutral-0);
        color: var(--color-neutral-900);
        border-bottom-left-radius: var(--border-radius-sm);
      }
      
      .chat-message.user .message-content {
        background: var(--color-primary-500);
        color: var(--color-neutral-0);
        border-bottom-right-radius: var(--border-radius-sm);
      }
      
      @keyframes slideUp {
        from {
          opacity: 0;
          transform: translateY(10px);
        }
        to {
          opacity: 1;
          transform: translateY(0);
        }
      }
    `}</style>
  </div>
);

// 进度条组件
const ProgressBar = ({ progress }) => (
  <div className="progress-container">
    <div className="progress-bar">
      <div 
        className="progress-fill" 
        style={{ width: `${progress}%` }}
      ></div>
    </div>
    <span className="progress-text">{progress}%</span>
    <style>{`
      .progress-container {
        display: flex;
        align-items: center;
        gap: var(--spacing-3);
        padding: var(--spacing-4) var(--spacing-5);
        background: var(--color-neutral-0);
        border-radius: var(--border-radius-lg);
        margin-bottom: var(--spacing-4);
      }
      
      .progress-bar {
        flex: 1;
        height: 8px;
        background: var(--color-neutral-100);
        border-radius: var(--border-radius-full);
        overflow: hidden;
      }
      
      .progress-fill {
        height: 100%;
        background: linear-gradient(90deg, var(--color-primary-500), #38bdf8);
        border-radius: var(--border-radius-full);
        transition: width var(--duration-slow) var(--easing-ease-out);
      }
      
      .progress-text {
        font-size: var(--font-size-sm);
        font-weight: var(--font-weight-medium);
        color: var(--color-neutral-600);
        min-width: 40px;
      }
    `}</style>
  </div>
);

// 快捷回复按钮组件
const QuickReply = ({ options, onSelect }) => (
  <div className="quick-replies">
    {options.map((option, index) => (
      <button 
        key={index}
        className="quick-reply-btn"
        onClick={() => onSelect(option)}
      >
        {option}
      </button>
    ))}
    <style>{`
      .quick-replies {
        display: flex;
        flex-wrap: wrap;
        gap: var(--spacing-2);
        margin-bottom: var(--spacing-4);
        padding: 0 var(--spacing-5);
      }
      
      .quick-reply-btn {
        padding: var(--spacing-2) var(--spacing-4);
        background: var(--color-neutral-0);
        border: 1px solid var(--color-neutral-300);
        border-radius: var(--border-radius-full);
        font-size: var(--font-size-sm);
        color: var(--color-neutral-600);
        cursor: pointer;
        transition: all var(--duration-fast) var(--easing-ease-out);
      }
      
      .quick-reply-btn:hover {
        background: var(--color-primary-50);
        border-color: var(--color-primary-500);
        color: var(--color-primary-500);
      }
    `}</style>
  </div>
);

// 简历预览卡片组件
const ResumePreview = ({ data }) => (
  <div className="resume-preview">
    <div className="preview-header">
      <FileText size={20} />
      <span>简历预览</span>
    </div>
    <div className="preview-content">
      {data.name && (
        <div className="preview-section">
          <h3>{data.name}</h3>
          <p>{data.email || data.phone || data.contact}</p>
        </div>
      )}
      {data.jobIntention && (
        <div className="preview-section">
          <h4>求职意向</h4>
          <p>{data.jobIntention}</p>
        </div>
      )}
      {data.education && (
        <div className="preview-section">
          <h4>教育背景</h4>
          <p>{data.education}</p>
        </div>
      )}
      {data.experience && (
        <div className="preview-section">
          <h4>工作经历</h4>
          <p>{data.experience}</p>
        </div>
      )}
      {data.skills && data.skills.length > 0 && (
        <div className="preview-section">
          <h4>专业技能</h4>
          <div className="skills-list">
            {data.skills.map((skill, index) => (
              <span key={index} className="skill-tag">{skill}</span>
            ))}
          </div>
        </div>
      )}
    </div>
    <style>{`
      .resume-preview {
        background: var(--color-neutral-0);
        border-radius: var(--border-radius-lg);
        box-shadow: var(--shadow-sm);
        margin-bottom: var(--spacing-4);
        overflow: hidden;
      }
      
      .preview-header {
        display: flex;
        align-items: center;
        gap: var(--spacing-2);
        padding: var(--spacing-4) var(--spacing-5);
        background: var(--color-neutral-50);
        border-bottom: 1px solid var(--color-neutral-200);
        font-size: var(--font-size-sm);
        font-weight: var(--font-weight-medium);
        color: var(--color-neutral-600);
      }
      
      .preview-content {
        padding: var(--spacing-5);
      }
      
      .preview-section {
        margin-bottom: var(--spacing-4);
      }
      
      .preview-section:last-child {
        margin-bottom: 0;
      }
      
      .preview-section h3 {
        margin: 0 0 var(--spacing-1) 0;
        font-size: var(--font-size-lg);
        font-weight: var(--font-weight-semibold);
        color: var(--color-neutral-900);
      }
      
      .preview-section h4 {
        margin: 0 0 var(--spacing-2) 0;
        font-size: var(--font-size-base);
        font-weight: var(--font-weight-medium);
        color: var(--color-neutral-900);
      }
      
      .preview-section p {
        margin: 0;
        font-size: var(--font-size-sm);
        color: var(--color-neutral-600);
        line-height: 1.5;
      }
      
      .skills-list {
        display: flex;
        flex-wrap: wrap;
        gap: var(--spacing-2);
      }
      
      .skill-tag {
        padding: var(--spacing-1) var(--spacing-3);
        background: var(--color-primary-100);
        color: var(--color-primary-700);
        border-radius: var(--border-radius-full);
        font-size: var(--font-size-xs);
      }
    `}</style>
  </div>
);

// AI建议卡片组件
const AISuggestionCard = ({ suggestions, score }) => (
  <div className="ai-suggestion-card">
    <div className="suggestion-header">
      <Sparkles size={16} />
      <span>AI智能建议</span>
      <div className="score-badge">
        评分: {score}/100
      </div>
    </div>
    <div className="suggestion-content">
      {suggestions.length > 0 ? (
        <ul className="suggestion-list">
          {suggestions.map((suggestion, index) => (
            <li key={index} className="suggestion-item">
              {suggestion}
            </li>
          ))}
        </ul>
      ) : (
        <p className="no-suggestions">
          🎉 简历内容很完整！继续保持这个质量。
        </p>
      )}
    </div>
    <style>{`
      .ai-suggestion-card {
        background: linear-gradient(135deg, var(--color-primary-50) 0%, var(--color-neutral-0) 100%);
        border: 1px solid var(--color-primary-200);
        border-radius: var(--border-radius-lg);
        margin-bottom: var(--spacing-4);
        overflow: hidden;
      }
      
      .suggestion-header {
        display: flex;
        align-items: center;
        gap: var(--spacing-2);
        padding: var(--spacing-4) var(--spacing-5);
        background: var(--color-primary-100);
        font-size: var(--font-size-sm);
        font-weight: var(--font-weight-medium);
        color: var(--color-primary-700);
      }
      
      .score-badge {
        margin-left: auto;
        padding: var(--spacing-1) var(--spacing-3);
        background: var(--color-primary-500);
        color: var(--color-neutral-0);
        border-radius: var(--border-radius-full);
        font-size: var(--font-size-xs);
        font-weight: var(--font-weight-semibold);
      }
      
      .suggestion-content {
        padding: var(--spacing-4) var(--spacing-5);
      }
      
      .suggestion-list {
        margin: 0;
        padding: 0;
        list-style: none;
      }
      
      .suggestion-item {
        padding: var(--spacing-2) 0;
        font-size: var(--font-size-sm);
        color: var(--color-neutral-600);
        border-bottom: 1px solid var(--color-neutral-200);
        position: relative;
        padding-left: var(--spacing-5);
      }
      
      .suggestion-item:before {
        content: "💡";
        position: absolute;
        left: 0;
        top: var(--spacing-2);
      }
      
      .suggestion-item:last-child {
        border-bottom: none;
      }
      
      .no-suggestions {
        margin: 0;
        text-align: center;
        color: var(--color-success-base);
        font-size: var(--font-size-sm);
      }
    `}</style>
  </div>
);

// 导航标签组件
const TabNavigation = ({ currentView, onViewChange }) => (
  <div className="tab-navigation">
    <button 
      className={`tab-btn ${currentView === 'chat' ? 'active' : ''}`}
      onClick={() => onViewChange('chat')}
    >
      <MessageCircle size={16} />
      对话制作
    </button>
    <button 
      className={`tab-btn ${currentView === 'preview' ? 'active' : ''}`}
      onClick={() => onViewChange('preview')}
    >
      <FileText size={16} />
      简历预览
    </button>
    <style>{`
      .tab-navigation {
        display: flex;
        background: var(--color-neutral-0);
        border-bottom: 1px solid var(--color-neutral-200);
        padding: 0 var(--spacing-5);
      }
      
      .tab-btn {
        display: flex;
        align-items: center;
        gap: var(--spacing-2);
        padding: var(--spacing-4) var(--spacing-5);
        background: none;
        border: none;
        font-size: var(--font-size-sm);
        color: var(--color-neutral-600);
        cursor: pointer;
        position: relative;
        transition: color var(--duration-fast) var(--easing-ease-out);
      }
      
      .tab-btn:hover {
        color: var(--color-primary-500);
      }
      
      .tab-btn.active {
        color: var(--color-primary-500);
        font-weight: var(--font-weight-medium);
      }
      
      .tab-btn.active:after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        height: 2px;
        background: var(--color-primary-500);
      }
    `}</style>
  </div>
);

// 主应用组件
const AIResumeApp = () => {
  const [messages, setMessages] = useState([]);
  const [inputValue, setInputValue] = useState('');
  const [isTyping, setIsTyping] = useState(false);
  const [currentStep, setCurrentStep] = useState(0);
  const [progress, setProgress] = useState(0);
  const [resumeData, setResumeData] = useState({});
  const [showQuickReplies, setShowQuickReplies] = useState(false);
  const [quickReplyOptions, setQuickReplyOptions] = useState([]);
  const [currentView, setCurrentView] = useState('chat');
  const [aiSuggestions, setAiSuggestions] = useState([]);
  const [resumeScore, setResumeScore] = useState(0);
  
  const messagesEndRef = useRef(null);

  // 对话流程配置
  const conversationFlow = [
    {
      id: 'welcome',
      aiMessage: '嗨！我是小云，你的专属简历助手 ✨\n\n我不会让你填一堆表格，咱们就像聊天一样，我来帮你梳理一份出色的简历。\n\n放轻松，想到什么说什么就行 😊',
      quickReplies: ['开始制作简历', '我不太会写简历', '了解一下功能'],
      dataField: null
    },
    {
      id: 'intention',
      aiMessage: '太好了！先随便聊聊，你最近在找什么样的工作呢？\n\n或者你可以先说说你现在的情况，我来了解一下你的背景~',
      quickReplies: ['应届毕业生', '想要跳槽', '转行求职'],
      dataField: 'jobIntention'
    },
    {
      id: 'basic_info',
      aiMessage: '很好！现在我需要知道怎么称呼你，以及如何联系你。\n\n可以告诉我你的姓名和联系方式吗？',
      quickReplies: [],
      dataField: 'contact'
    },
    {
      id: 'education',
      aiMessage: '听起来很不错！你是学什么专业的？在学校有没有什么特别的成就或者印象深刻的经历？',
      quickReplies: ['本科毕业', '研究生学历', '专科学历'],
      dataField: 'education'
    },
    {
      id: 'experience',
      aiMessage: '这个专业很有前景！现在说说你的工作经历吧。\n\n有没有什么让你特别有成就感的项目或者成果？不用太正式，就像朋友聊天一样~',
      quickReplies: ['刚毕业没经验', '有1-3年经验', '有丰富经验'],
      dataField: 'experience'
    },
    {
      id: 'completion',
      aiMessage: '哇，听起来你的经历很丰富！让我帮你整理一下...\n\n现在简历的主要内容都有了，我来为你生成一份专业的简历吧！',
      quickReplies: ['生成简历', '再补充一些', '重新开始'],
      dataField: null
    }
  ];

  // 滚动到底部
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  // 初始化对话
  useEffect(() => {
    const welcomeMessage = conversationFlow[0];
    setMessages([{
      id: Date.now(),
      text: welcomeMessage.aiMessage,
      isAI: true
    }]);
    setQuickReplyOptions(welcomeMessage.quickReplies);
    setShowQuickReplies(true);
  }, []);

  // AI回复延迟效果
  const addAIMessage = (text, delay = 1000) => {
    setIsTyping(true);
    setTimeout(() => {
      setIsTyping(false);
      setMessages(prev => [...prev, {
        id: Date.now(),
        text: text,
        isAI: true
      }]);
    }, delay);
  };

  // 处理对话逻辑
  const handleConversationLogic = (userMessage) => {
    const step = conversationFlow[currentStep];
    
    // AI智能信息提取
    if (step.id === 'basic_info') {
      const contactInfo = AIAnalyzer.extractContactInfo(userMessage);
      setResumeData(prev => ({
        ...prev,
        name: contactInfo.name || prev.name,
        email: contactInfo.email,
        phone: contactInfo.phone,
        contact: userMessage
      }));
    }
    
    if (step.id === 'experience') {
      const analysis = AIAnalyzer.analyzeExperience(userMessage);
      setResumeData(prev => ({
        ...prev,
        experience: userMessage,
        achievements: analysis.achievements,
        skills: [...(prev.skills || []), ...analysis.skills]
      }));
    }
    
    // 更新简历评分
    setTimeout(() => {
      const newResumeData = { ...resumeData };
      if (step.dataField) {
        newResumeData[step.dataField] = userMessage;
      }
      
      const score = ResumeOptimizer.calculateScore(newResumeData);
      setResumeScore(score);
      
      // 生成AI建议
      if (score >= 50) {
        const suggestions = AIAnalyzer.generateSuggestions(newResumeData);
        setAiSuggestions(suggestions);
      }
    }, 500);
  };

  // 处理用户输入
  const handleSendMessage = (message) => {
    const userMessage = message || inputValue.trim();
    if (!userMessage) return;

    // 添加用户消息
    setMessages(prev => [...prev, {
      id: Date.now(),
      text: userMessage,
      isAI: false
    }]);

    setInputValue('');
    setShowQuickReplies(false);

    // 更新简历数据
    const currentFlow = conversationFlow[currentStep];
    if (currentFlow.dataField) {
      setResumeData(prev => ({
        ...prev,
        [currentFlow.dataField]: userMessage
      }));
    }

    // 处理特殊逻辑
    handleConversationLogic(userMessage);

    // 进入下一步
    if (currentStep < conversationFlow.length - 1) {
      const nextStep = currentStep + 1;
      const nextFlow = conversationFlow[nextStep];
      
      setTimeout(() => {
        addAIMessage(nextFlow.aiMessage, 800);
        setCurrentStep(nextStep);
        setProgress(Math.round((nextStep / (conversationFlow.length - 1)) * 100));
        
        setTimeout(() => {
          setQuickReplyOptions(nextFlow.quickReplies);
          setShowQuickReplies(nextFlow.quickReplies.length > 0);
        }, 1200);
      }, 500);
    }
  };

  // 快捷回复处理
  const handleQuickReply = (option) => {
    handleSendMessage(option);
  };

  // 下载简历
  const handleDownload = () => {
    addAIMessage('简历生成中... 马上就好！', 500);
    setTimeout(() => {
      addAIMessage('🎉 恭喜！你的简历已经生成完成！\n\n这份简历基于我们的对话内容，突出了你的优势和亮点。如果你需要针对特定岗位优化，随时告诉我职位要求，我来帮你调整！', 1000);
    }, 1500);
  };

  // 视图切换处理
  const handleViewChange = (view) => {
    setCurrentView(view);
  };

  return (
    <div className="app-container">
      <DesignTokens />
      
      {/* 头部 */}
      <header className="app-header">
        <div className="header-content">
          <div className="header-left">
            <div className="logo">
              <Sparkles size={24} />
              <span>AI简历助手</span>
            </div>
          </div>
          <div className="header-right">
            <button className="icon-btn">
              <User size={20} />
            </button>
          </div>
        </div>
      </header>

      {/* 标签导航 */}
      <TabNavigation currentView={currentView} onViewChange={handleViewChange} />

      {/* 主要内容区域 */}
      <main className="main-content">
        {currentView === 'chat' && (
          <>
            {/* 进度指示器 */}
            <ProgressBar progress={progress} />

            {/* AI建议卡片 */}
            {(aiSuggestions.length > 0 || resumeScore > 0) && (
              <AISuggestionCard suggestions={aiSuggestions} score={resumeScore} />
            )}

            {/* 简历预览 */}
            {Object.keys(resumeData).length > 0 && (
              <ResumePreview data={resumeData} />
            )}

            {/* 对话消息区域 */}
            <div className="messages-container">
              {messages.map((message) => (
                <ChatBubble
                  key={message.id}
                  message={message.text}
                  isAI={message.isAI}
                />
              ))}
              
              {isTyping && (
                <ChatBubble isAI={true} isTyping={true} />
              )}
              
              <div ref={messagesEndRef} />
            </div>

            {/* 快捷回复 */}
            {showQuickReplies && quickReplyOptions.length > 0 && (
              <QuickReply 
                options={quickReplyOptions}
                onSelect={handleQuickReply}
              />
            )}
          </>
        )}

        {currentView === 'preview' && (
          <div className="preview-view">
            <div className="preview-header">
              <h2>简历完整预览</h2>
              <div className="preview-score">
                完整度: {resumeScore}%
              </div>
            </div>
            <ResumePreview data={resumeData} />
            {aiSuggestions.length > 0 && (
              <AISuggestionCard suggestions={aiSuggestions} score={resumeScore} />
            )}
          </div>
        )}
      </main>

      {/* 底部输入区域 */}
      <footer className="input-area">
        {currentView === 'chat' && (
          <>
            <div className="input-container">
              <input
                type="text"
                value={inputValue}
                onChange={(e) => setInputValue(e.target.value)}
                onKeyPress={(e) => e.key === 'Enter' && handleSendMessage()}
                placeholder="输入消息..."
                className="message-input"
              />
              <button 
                onClick={() => handleSendMessage()}
                className="send-btn"
                disabled={!inputValue.trim()}
              >
                <Send size={20} />
              </button>
            </div>
            
            {progress === 100 && (
              <div className="action-buttons">
                <button className="download-btn" onClick={handleDownload}>
                  <Download size={16} />
                  下载简历
                </button>
              </div>
            )}
          </>
        )}
        
        {currentView === 'preview' && (
          <div className="action-buttons">
            <button className="download-btn" onClick={handleDownload}>
              <Download size={16} />
              下载简历
            </button>
            <button 
              className="optimize-nav-btn"
              onClick={() => setCurrentView('chat')}
            >
              <MessageCircle size={16} />
              继续对话
            </button>
          </div>
        )}
      </footer>

      <style>{`
        .app-container {
          display: flex;
          flex-direction: column;
          height: 100vh;
          max-width: 800px;
          margin: 0 auto;
          background: var(--color-neutral-0);
          box-shadow: var(--shadow-lg);
        }
        
        .app-header {
          background: var(--color-neutral-0);
          border-bottom: 1px solid var(--color-neutral-200);
          padding: var(--spacing-4) var(--spacing-5);
        }
        
        .header-content {
          display: flex;
          justify-content: space-between;
          align-items: center;
        }
        
        .logo {
          display: flex;
          align-items: center;
          gap: var(--spacing-2);
          font-size: var(--font-size-lg);
          font-weight: var(--font-weight-semibold);
          color: var(--color-primary-500);
        }
        
        .icon-btn {
          width: 40px;
          height: 40px;
          border-radius: var(--border-radius-full);
          border: none;
          background: var(--color-neutral-100);
          color: var(--color-neutral-600);
          display: flex;
          align-items: center;
          justify-content: center;
          cursor: pointer;
          transition: all var(--duration-fast) var(--easing-ease-out);
        }
        
        .icon-btn:hover {
          background: var(--color-neutral-200);
        }
        
        .main-content {
          flex: 1;
          overflow-y: auto;
          padding: var(--spacing-4) var(--spacing-5);
          background: var(--color-neutral-50);
        }
        
        .messages-container {
          display: flex;
          flex-direction: column;
          gap: var(--spacing-2);
          margin-bottom: var(--spacing-6);
        }
        
        .input-area {
          background: var(--color-neutral-0);
          border-top: 1px solid var(--color-neutral-200);
          padding: var(--spacing-4) var(--spacing-5);
        }
        
        .input-container {
          display: flex;
          gap: var(--spacing-3);
          align-items: center;
        }
        
        .message-input {
          flex: 1;
          padding: var(--spacing-3) var(--spacing-4);
          border: 1px solid var(--color-neutral-300);
          border-radius: var(--border-radius-full);
          font-size: var(--font-size-base);
          outline: none;
          transition: border-color var(--duration-normal) var(--easing-ease-out);
        }
        
        .message-input:focus {
          border-color: var(--color-primary-500);
          box-shadow: 0 0 0 3px var(--color-primary-100);
        }
        
        .send-btn {
          width: 44px;
          height: 44px;
          border-radius: var(--border-radius-full);
          border: none;
          background: var(--color-primary-500);
          color: var(--color-neutral-0);
          display: flex;
          align-items: center;
          justify-content: center;
          cursor: pointer;
          transition: all var(--duration-fast) var(--easing-ease-out);
        }
        
        .send-btn:hover:not(:disabled) {
          background: #0284c7;
          transform: translateY(-1px);
        }
        
        .send-btn:disabled {
          background: var(--color-neutral-300);
          cursor: not-allowed;
        }
        
        .action-buttons {
          margin-top: var(--spacing-4);
          display: flex;
          justify-content: center;
          gap: var(--spacing-3);
        }
        
        .download-btn {
          display: flex;
          align-items: center;
          gap: var(--spacing-2);
          padding: var(--spacing-3) var(--spacing-6);
          background: var(--color-success-base);
          color: var(--color-neutral-0);
          border: none;
          border-radius: var(--border-radius-md);
          font-size: var(--font-size-base);
          font-weight: var(--font-weight-medium);
          cursor: pointer;
          transition: all var(--duration-fast) var(--easing-ease-out);
        }
        
        .download-btn:hover {
          background: #15803d;
          transform: translateY(-1px);
          box-shadow: var(--shadow-md);
        }
        
        .optimize-nav-btn {
          display: flex;
          align-items: center;
          gap: var(--spacing-2);
          padding: var(--spacing-3) var(--spacing-6);
          background: var(--color-primary-500);
          color: var(--color-neutral-0);
          border: none;
          border-radius: var(--border-radius-md);
          font-size: var(--font-size-base);
          font-weight: var(--font-weight-medium);
          cursor: pointer;
          transition: all var(--duration-fast) var(--easing-ease-out);
        }
        
        .optimize-nav-btn:hover {
          background: #0284c7;
          transform: translateY(-1px);
          box-shadow: var(--shadow-md);
        }
        
        .preview-view {
          animation: fadeIn var(--duration-normal) var(--easing-ease-out);
        }
        
        .preview-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: var(--spacing-6);
          padding-bottom: var(--spacing-4);
          border-bottom: 1px solid var(--color-neutral-200);
        }
        
        .preview-header h2 {
          margin: 0;
          font-size: var(--font-size-xl);
          font-weight: var(--font-weight-semibold);
          color: var(--color-neutral-900);
        }
        
        .preview-score {
          padding: var(--spacing-2) var(--spacing-4);
          background: var(--color-primary-100);
          color: var(--color-primary-700);
          border-radius: var(--border-radius-full);
          font-size: var(--font-size-sm);
          font-weight: var(--font-weight-medium);
        }
        
        @keyframes fadeIn {
          from {
            opacity: 0;
            transform: translateY(10px);
          }
          to {
            opacity: 1;
            transform: translateY(0);
          }
        }
        
        /* 响应式设计 */
        @media (max-width: 768px) {
          .app-container {
            height: 100vh;
          }
          
          .main-content {
            padding: var(--spacing-3) var(--spacing-4);
          }
          
          .input-area {
            padding: var(--spacing-3) var(--spacing-4);
          }
          
          .action-buttons {
            flex-direction: column;
            gap: var(--spacing-2);
          }
          
          .download-btn, .optimize-nav-btn {
            width: 100%;
            justify-content: center;
          }
        }
      `}</style>
    </div>
  );
};

export default AIResumeApp;