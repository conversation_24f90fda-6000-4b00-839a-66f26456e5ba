# AI简历助手产品开发文档
## 基于人性化故事对话的求职转化系统

> "简约就是复杂的极致。" — 乔布斯  
> "好的设计不只是看起来好，更要用起来好。" — 乔纳森·艾维

---

## 1. 产品愿景与哲学

### 1.1 产品使命
**让每个人都能用最自然的方式，讲述属于自己的职业故事，获得心仪的工作机会。**

### 1.2 设计哲学

#### 乔布斯的极简主义
- **Less is More**：界面极简，功能聚焦，每个元素都有存在的理由
- **用户至上**：技术服务于体验，而非展示技术本身
- **完美主义**：每个细节都经过精心打磨

#### 乔纳森的人性化设计
- **直觉交互**：用户无需学习，自然而然地知道如何使用
- **情感连接**：产品与用户建立情感纽带，不只是工具，更是伙伴
- **无缝体验**：技术在背后默默工作，用户感受到的是魔法般的体验

### 1.3 核心理念

#### "故事驱动，而非表格填写"
传统简历制作是冰冷的表格填写，我们要做的是温暖的故事倾听。每个人的职业经历都是一个独特的故事，我们的AI不是信息收集器，而是专业的故事编辑师。

#### "人性化对话，而非机械问答"
不是"请输入您的姓名"，而是"嗨，我想了解一下你的故事，先从你的名字开始吧"。每句话都充满人情味，让用户感受到被理解和关心。

#### "智能匹配，而非一简投天下"
基于DeepSeek强大的语言理解能力，深度分析职位需求和用户特质，为每个岗位量身定制简历，提升求职成功率。

---

## 2. 产品架构设计

### 2.1 整体架构

```mermaid
graph TB
    A[微信小程序前端] --> B[腾讯云开发]
    B --> C[DeepSeek AI引擎]
    B --> D[云数据库]
    B --> E[云存储]
    B --> F[云函数]
    
    C --> G[对话理解模块]
    C --> H[简历优化模块]
    C --> I[职位匹配模块]
    
    F --> J[用户管理]
    F --> K[简历生成]
    F --> L[AI分析]
    F --> M[文件处理]
```

### 2.2 技术选型

#### 前端技术栈
- **微信小程序原生框架**：保证最佳性能和用户体验
- **组件化架构**：高度复用，易于维护
- **响应式设计**：适配不同设备尺寸

#### 后端技术栈
- **腾讯云开发**：serverless架构，快速开发部署
- **Node.js云函数**：灵活的业务逻辑处理
- **MongoDB数据库**：灵活的文档存储

#### AI能力
- **DeepSeek模型**：强大的中文理解和生成能力
- **多轮对话管理**：保持上下文连贯性
- **意图识别**：准确理解用户表达

---

## 3. 核心功能设计

### 3.1 故事化对话引擎

#### 设计理念
"让AI成为最好的倾听者和故事编辑师"

#### 对话流程设计

```javascript
// 对话阶段定义
const CONVERSATION_STAGES = {
  GREETING: {
    title: '初次见面',
    purpose: '建立信任，了解用户背景',
    style: '温暖、友好、有耐心',
    example: '嗨！我是小简，你的专属简历顾问。不用紧张，我们就像朋友聊天一样，我来帮你梳理出一份让HR眼前一亮的简历。'
  },
  
  STORY_EXPLORATION: {
    title: '故事探索',
    purpose: '深度挖掘用户经历和亮点',
    style: '好奇、鼓励、引导深入',
    example: '这个项目听起来很有挑战性！你当时是怎么想到这个解决方案的？过程中有什么让你特别有成就感的时刻吗？'
  },
  
  VALUE_DISCOVERY: {
    title: '价值发现',
    purpose: '帮助用户认识自己的价值',
    style: '肯定、专业、有洞察力',
    example: '从你刚才的描述中，我看到了你出色的问题解决能力和团队协作精神。这在你目标的产品经理岗位上是非常宝贵的品质。'
  },
  
  STORY_POLISHING: {
    title: '故事润色',
    purpose: '优化表达，突出亮点',
    style: '专业、建设性、有技巧',
    example: '我建议把这个成果表达得更具体一些。比如，"提升用户活跃度"可以说成"通过数据分析和产品优化，三个月内将日活用户提升了35%"，这样更有说服力。'
  }
}
```

#### 对话策略

##### 1. 情感化开场
```javascript
const GREETING_STRATEGIES = [
  {
    userType: 'fresh_graduate',
    message: '刚毕业啊！那一定很激动也很紧张吧。别担心，每个人都是从这一步开始的。你在学校期间有什么特别有意思的经历吗？',
    tone: 'encouraging'
  },
  {
    userType: 'career_changer',
    message: '转行需要很大勇气呢！是什么让你想要做出这个改变的？说说你的想法，我来帮你找到转行的优势。',
    tone: 'supportive'
  },
  {
    userType: 'experienced',
    message: '有这么多年经验，你一定有很多精彩的职场故事。哪个项目或经历让你觉得最有成就感？',
    tone: 'respectful'
  }
]
```

##### 2. 深度挖掘技巧
```javascript
const DIGGING_TECHNIQUES = {
  // STAR法则引导
  STAR_METHOD: {
    S: '当时的情况是怎样的？',
    T: '你需要完成什么任务？',
    A: '你具体做了什么？',
    R: '最后的结果如何？'
  },
  
  // 情感共鸣
  EMOTIONAL_CONNECTION: [
    '听起来那时候压力很大，你是怎么处理的？',
    '这个成果让你有什么感受？',
    '如果重新来一次，你会怎么做？'
  ],
  
  // 价值发现
  VALUE_DISCOVERY: [
    '你觉得这个经历最大的收获是什么？',
    '同事们是怎么评价你的？',
    '你在团队中通常扮演什么角色？'
  ]
}
```

### 3.2 DeepSeek AI集成

#### AI对话管理
```javascript
// DeepSeek API配置
const DEEPSEEK_CONFIG = {
  apiKey: process.env.DEEPSEEK_API_KEY,
  model: 'deepseek-chat',
  baseURL: 'https://api.deepseek.com/v1',
  temperature: 0.7, // 保持一定创造性
  maxTokens: 2000,
  systemPrompt: `
    你是一位专业的简历顾问，具有以下特质：
    1. 温暖友善，像朋友一样交流
    2. 专业敏锐，能发现用户的亮点和价值
    3. 耐心引导，帮助用户深入挖掘经历
    4. 善于鼓励，让用户重新认识自己的价值
    
    你的任务是通过自然对话：
    - 收集用户的职业经历和技能
    - 挖掘用户的核心竞争力
    - 帮助用户用更好的方式表达自己
    - 针对目标职位优化简历内容
    
    对话风格：
    - 用"你"而不是"您"
    - 多用鼓励性词语
    - 适当使用emoji增加亲和力
    - 避免机械化和模板化表达
  `
}

// 对话处理函数
async function processConversation(userMessage, conversationHistory, currentStage) {
  const messages = [
    { role: 'system', content: DEEPSEEK_CONFIG.systemPrompt },
    ...conversationHistory,
    { role: 'user', content: userMessage }
  ]
  
  try {
    const response = await deepseekAPI.chat.completions.create({
      model: DEEPSEEK_CONFIG.model,
      messages: messages,
      temperature: DEEPSEEK_CONFIG.temperature,
      max_tokens: DEEPSEEK_CONFIG.maxTokens
    })
    
    const aiResponse = response.choices[0].message.content
    
    // 提取结构化信息
    const extractedInfo = await extractStructuredInfo(userMessage, aiResponse)
    
    return {
      aiMessage: aiResponse,
      extractedInfo: extractedInfo,
      nextStage: determineNextStage(currentStage, extractedInfo)
    }
  } catch (error) {
    console.error('DeepSeek API调用失败:', error)
    return getFallbackResponse(currentStage)
  }
}
```

#### 信息提取与结构化
```javascript
// 使用DeepSeek进行信息提取
async function extractStructuredInfo(userMessage, context) {
  const extractionPrompt = `
    从以下对话中提取结构化信息，输出JSON格式：
    
    用户消息: "${userMessage}"
    上下文: "${context}"
    
    需要提取的信息类型：
    - 个人信息：姓名、联系方式、年龄等
    - 教育背景：学校、专业、学历、成绩等
    - 工作经历：公司、职位、时间、职责、成果
    - 项目经验：项目名称、角色、技术栈、成果
    - 技能特长：专业技能、工具使用、语言能力
    - 个人特质：性格特点、工作风格、价值观
    
    输出格式：
    {
      "extractedData": {
        "personalInfo": {},
        "education": {},
        "workExperience": [],
        "projects": [],
        "skills": [],
        "personalTraits": []
      },
      "confidence": 0.8, // 提取置信度
      "needsMoreInfo": [] // 需要进一步了解的信息
    }
  `
  
  const response = await deepseekAPI.chat.completions.create({
    model: 'deepseek-chat',
    messages: [{ role: 'user', content: extractionPrompt }],
    temperature: 0.1 // 降低随机性，提高准确性
  })
  
  try {
    return JSON.parse(response.choices[0].message.content)
  } catch (error) {
    console.error('信息提取失败:', error)
    return { extractedData: {}, confidence: 0, needsMoreInfo: [] }
  }
}
```

### 3.3 职位匹配优化系统

#### 设计理念
"为每个职位量身定制，让简历成为求职者的最佳代言人"

#### JD分析引擎
```javascript
// 职位描述分析
async function analyzeJobDescription(jobDescription) {
  const analysisPrompt = `
    请深度分析以下职位描述，提取关键信息：
    
    职位描述：
    ${jobDescription}
    
    请从以下维度进行分析：
    1. 必需技能 vs 加分技能
    2. 工作内容和职责
    3. 公司文化和价值观
    4. 成长机会和发展路径
    5. 理想候选人画像
    
    输出JSON格式，包含：
    - requiredSkills: 必需技能列表
    - preferredSkills: 优选技能列表
    - responsibilities: 主要职责
    - companyCulture: 公司文化关键词
    - candidateProfile: 理想候选人特征
    - keywords: 重要关键词列表
    - salaryRange: 薪资范围（如果提及）
    - workMode: 工作模式（远程/现场/混合）
  `
  
  const response = await deepseekAPI.chat.completions.create({
    model: 'deepseek-chat',
    messages: [{ role: 'user', content: analysisPrompt }],
    temperature: 0.2
  })
  
  return JSON.parse(response.choices[0].message.content)
}

// 简历优化建议
async function generateOptimizationSuggestions(resumeData, jobAnalysis) {
  const optimizationPrompt = `
    基于以下简历数据和职位分析，生成针对性的优化建议：
    
    简历数据：
    ${JSON.stringify(resumeData, null, 2)}
    
    职位分析：
    ${JSON.stringify(jobAnalysis, null, 2)}
    
    请生成具体的优化建议，包括：
    1. 技能匹配度分析
    2. 经历重新排序建议
    3. 关键词优化建议
    4. 成果表达优化
    5. 个人特质突出建议
    
    输出格式：
    {
      "matchScore": 85, // 匹配度评分
      "strengthsAlignment": [], // 优势对齐分析
      "improvementAreas": [], // 需要改进的地方
      "keywordOptimization": [], // 关键词优化建议
      "contentRestructure": [], // 内容重构建议
      "personalityHighlight": [] // 个性化突出建议
    }
  `
  
  const response = await deepseekAPI.chat.completions.create({
    model: 'deepseek-chat',
    messages: [{ role: 'user', content: optimizationPrompt }],
    temperature: 0.3
  })
  
  return JSON.parse(response.choices[0].message.content)
}
```

#### 智能简历生成
```javascript
// 基于目标职位生成定制简历
async function generateCustomizedResume(userData, jobDescription, template) {
  const generationPrompt = `
    请基于以下信息生成一份针对性的简历：
    
    用户数据：
    ${JSON.stringify(userData, null, 2)}
    
    目标职位：
    ${jobDescription}
    
    简历模板：${template}
    
    生成要求：
    1. 突出与职位最相关的经历和技能
    2. 使用职位描述中的关键词
    3. 优化成果表达，量化具体数据
    4. 体现个人特质和价值观匹配
    5. 确保语言专业且有感染力
    
    输出完整的简历内容，包括：
    - 个人简介
    - 核心技能
    - 工作经历
    - 项目经验
    - 教育背景
    - 个人特质
  `
  
  const response = await deepseekAPI.chat.completions.create({
    model: 'deepseek-chat',
    messages: [{ role: 'user', content: generationPrompt }],
    temperature: 0.4,
    max_tokens: 3000
  })
  
  return response.choices[0].message.content
}
```

---

## 4. 微信小程序前端开发

### 4.1 项目结构

```
miniprogram/
├── pages/
│   ├── index/              # 启动页
│   │   ├── index.js
│   │   ├── index.wxml
│   │   ├── index.wxss
│   │   └── index.json
│   ├── chat/               # 对话页面
│   │   ├── chat.js
│   │   ├── chat.wxml
│   │   ├── chat.wxss
│   │   └── chat.json
│   ├── preview/            # 简历预览
│   ├── optimize/           # 优化建议
│   └── profile/            # 个人中心
├── components/
│   ├── chat-bubble/        # 对话气泡
│   ├── typing-indicator/   # 输入状态
│   ├── progress-ring/      # 进度环
│   ├── skill-tag/          # 技能标签
│   └── resume-card/        # 简历卡片
├── utils/
│   ├── request.js          # 网络请求
│   ├── storage.js          # 存储管理
│   ├── constants.js        # 常量定义
│   └── format.js           # 格式化工具
├── styles/
│   ├── variables.wxss      # 设计变量
│   ├── base.wxss          # 基础样式
│   └── components.wxss     # 组件样式
└── images/                 # 图片资源
```

### 4.2 设计系统实现

#### styles/variables.wxss
```css
/* 设计令牌 - 乔纳森风格的精致设计 */

/* 颜色系统 - 温暖而专业 */
:root {
  /* 主色调 - 温暖的蓝色，传达信任和专业 */
  --primary-50: #f0f9ff;
  --primary-100: #e0f2fe;
  --primary-500: #0ea5e9;
  --primary-600: #0284c7;
  --primary-700: #0369a1;
  
  /* 中性色 - 优雅的灰度系统 */
  --neutral-0: #ffffff;
  --neutral-50: #fafafa;
  --neutral-100: #f5f5f5;
  --neutral-200: #e5e5e5;
  --neutral-600: #525252;
  --neutral-900: #171717;
  
  /* 功能色 - 情感化的色彩表达 */
  --success-500: #22c55e;
  --warning-500: #f59e0b;
  --error-500: #ef4444;
  
  /* 字体系统 - 层次分明的信息架构 */
  --font-size-xs: 24rpx;
  --font-size-sm: 28rpx;
  --font-size-base: 32rpx;
  --font-size-lg: 36rpx;
  --font-size-xl: 40rpx;
  --font-size-2xl: 48rpx;
  
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  
  /* 间距系统 - 和谐的空间节奏 */
  --spacing-1: 8rpx;
  --spacing-2: 16rpx;
  --spacing-3: 24rpx;
  --spacing-4: 32rpx;
  --spacing-5: 40rpx;
  --spacing-6: 48rpx;
  --spacing-8: 64rpx;
  --spacing-10: 80rpx;
  
  /* 圆角系统 - 友好的边界处理 */
  --radius-sm: 8rpx;
  --radius-md: 16rpx;
  --radius-lg: 24rpx;
  --radius-xl: 32rpx;
  --radius-full: 9999rpx;
  
  /* 阴影系统 - 深度和层次感 */
  --shadow-sm: 0 2rpx 8rpx 0 rgba(0, 0, 0, 0.1);
  --shadow-md: 0 8rpx 16rpx -2rpx rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 20rpx 40rpx -6rpx rgba(0, 0, 0, 0.1);
  
  /* 动画系统 - 流畅的交互反馈 */
  --duration-fast: 150ms;
  --duration-normal: 200ms;
  --duration-slow: 300ms;
  --easing-ease-out: cubic-bezier(0, 0, 0.2, 1);
}
```

### 4.3 核心组件开发

#### 对话气泡组件 (chat-bubble)
```xml
<!-- components/chat-bubble/chat-bubble.wxml -->
<view class="chat-bubble {{isAI ? 'ai' : 'user'}} {{typing ? 'typing' : ''}}">
  <view wx:if="{{isAI}}" class="avatar">
    <image src="/images/ai-avatar.png" class="avatar-image" />
  </view>
  
  <view class="message-content">
    <view wx:if="{{typing}}" class="typing-indicator">
      <view class="typing-dot"></view>
      <view class="typing-dot"></view>
      <view class="typing-dot"></view>
    </view>
    <rich-text wx:else nodes="{{message}}" class="message-text"></rich-text>
  </view>
  
  <view wx:if="{{!isAI}}" class="user-indicator"></view>
</view>
```

```css
/* components/chat-bubble/chat-bubble.wxss */
.chat-bubble {
  display: flex;
  align-items: flex-end;
  margin-bottom: var(--spacing-4);
  animation: slideUp var(--duration-normal) var(--easing-ease-out);
}

.chat-bubble.user {
  flex-direction: row-reverse;
  justify-content: flex-start;
}

.avatar {
  width: 64rpx;
  height: 64rpx;
  border-radius: var(--radius-full);
  background: linear-gradient(135deg, var(--primary-500), var(--primary-600));
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: var(--spacing-3);
  box-shadow: var(--shadow-sm);
}

.avatar-image {
  width: 40rpx;
  height: 40rpx;
}

.message-content {
  max-width: 70%;
  padding: var(--spacing-4) var(--spacing-5);
  border-radius: var(--radius-xl);
  position: relative;
  box-shadow: var(--shadow-sm);
}

.chat-bubble.ai .message-content {
  background: var(--neutral-0);
  border-bottom-left-radius: var(--radius-sm);
}

.chat-bubble.user .message-content {
  background: var(--primary-500);
  color: var(--neutral-0);
  border-bottom-right-radius: var(--radius-sm);
  margin-left: var(--spacing-3);
}

.message-text {
  font-size: var(--font-size-base);
  line-height: 1.6;
}

.typing-indicator {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  padding: var(--spacing-2) 0;
}

.typing-dot {
  width: 12rpx;
  height: 12rpx;
  border-radius: var(--radius-full);
  background: var(--neutral-400);
  animation: typingPulse 1.4s ease-in-out infinite;
}

.typing-dot:nth-child(2) { animation-delay: 0.2s; }
.typing-dot:nth-child(3) { animation-delay: 0.4s; }

@keyframes typingPulse {
  0%, 60%, 100% {
    opacity: 0.4;
    transform: scale(1);
  }
  30% {
    opacity: 1;
    transform: scale(1.2);
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
```

```javascript
// components/chat-bubble/chat-bubble.js
Component({
  properties: {
    message: {
      type: String,
      value: ''
    },
    isAI: {
      type: Boolean,
      value: true
    },
    typing: {
      type: Boolean,
      value: false
    }
  },
  
  data: {},
  
  methods: {}
})
```

#### 对话页面核心逻辑
```javascript
// pages/chat/chat.js
const app = getApp()

Page({
  data: {
    messages: [],
    inputValue: '',
    isTyping: false,
    currentStage: 'greeting',
    resumeData: {},
    conversationId: null,
    quickReplies: []
  },
  
  onLoad(options) {
    this.initializeChat()
  },
  
  // 初始化对话
  initializeChat() {
    const welcomeMessage = {
      id: Date.now(),
      content: '嗨！我是小简，你的专属简历顾问 ✨\n\n不用担心不知道怎么写简历，我们就像朋友聊天一样。我会帮你发现自己的亮点，讲出属于你的职业故事。\n\n准备好了吗？先从你最近在找什么样的工作开始聊吧～',
      isAI: true,
      timestamp: Date.now()
    }
    
    this.setData({
      messages: [welcomeMessage],
      quickReplies: ['我是应届毕业生', '我想要跳槽', '我正在转行']
    })
    
    this.createConversation()
  },
  
  // 创建对话记录
  async createConversation() {
    try {
      const result = await wx.cloud.callFunction({
        name: 'conversationManage',
        data: {
          action: 'create',
          stage: this.data.currentStage
        }
      })
      
      this.setData({
        conversationId: result.result.conversationId
      })
    } catch (error) {
      console.error('创建对话失败:', error)
    }
  },
  
  // 发送消息
  async sendMessage(content) {
    const userMessage = {
      id: Date.now(),
      content: content,
      isAI: false,
      timestamp: Date.now()
    }
    
    this.setData({
      messages: [...this.data.messages, userMessage],
      inputValue: '',
      isTyping: true,
      quickReplies: []
    })
    
    // 滚动到底部
    this.scrollToBottom()
    
    try {
      // 调用AI处理函数
      const result = await wx.cloud.callFunction({
        name: 'aiChat',
        data: {
          message: content,
          conversationId: this.data.conversationId,
          currentStage: this.data.currentStage,
          resumeData: this.data.resumeData
        }
      })
      
      const { aiResponse, extractedData, nextStage, suggestions } = result.result
      
      setTimeout(() => {
        const aiMessage = {
          id: Date.now(),
          content: aiResponse,
          isAI: true,
          timestamp: Date.now()
        }
        
        this.setData({
          messages: [...this.data.messages, aiMessage],
          isTyping: false,
          currentStage: nextStage,
          resumeData: { ...this.data.resumeData, ...extractedData },
          quickReplies: suggestions || []
        })
        
        this.scrollToBottom()
      }, 1000) // 模拟思考时间
      
    } catch (error) {
      console.error('发送消息失败:', error)
      this.handleError()
    }
  },
  
  // 处理输入
  onInput(e) {
    this.setData({
      inputValue: e.detail.value
    })
  },
  
  // 发送按钮点击
  onSend() {
    const content = this