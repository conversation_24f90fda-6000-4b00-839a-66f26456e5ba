<!--components/resume/skill-tag/index.wxml-->
<view 
  class="{{getTagClass()}} {{customClass}}"
  bindtap="onTap"
>
  <!-- 技能名称 -->
  <text class="skill-name">{{skill}}</text>
  
  <!-- 技能等级星星 -->
  <view wx:if="{{level > 0}}" class="skill-level">
    <view 
      class="star {{star.filled ? 'star-filled' : 'star-empty'}}"
      wx:for="{{stars}}" 
      wx:key="index"
      wx:for-item="star"
    >
      ⭐
    </view>
  </view>
  
  <!-- 删除按钮 -->
  <view wx:if="{{closable}}" class="close-btn" bindtap="onClose">
    <text class="close-icon">×</text>
  </view>
</view>
