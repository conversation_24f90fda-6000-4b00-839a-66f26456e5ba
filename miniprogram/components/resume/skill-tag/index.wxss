/* components/resume/skill-tag/index.wxss */

.skill-tag {
  display: inline-flex;
  align-items: center;
  border-radius: 32rpx;
  font-weight: 500;
  transition: all 0.2s ease;
  position: relative;
}

/* 标签大小 */
.tag-small {
  padding: 8rpx 16rpx;
  font-size: 20rpx;
}

.tag-medium {
  padding: 12rpx 20rpx;
  font-size: 24rpx;
}

.tag-large {
  padding: 16rpx 24rpx;
  font-size: 28rpx;
}

/* 标签类型 */
.tag-default {
  background: #f1f5f9;
  color: #475569;
  border: 1px solid #e2e8f0;
}

.tag-primary {
  background: linear-gradient(135deg, #0ea5e9, #3b82f6);
  color: #ffffff;
  border: 1px solid #0ea5e9;
}

.tag-success {
  background: #dcfce7;
  color: #16a34a;
  border: 1px solid #bbf7d0;
}

.tag-warning {
  background: #fef3c7;
  color: #d97706;
  border: 1px solid #fed7aa;
}

.tag-danger {
  background: #fef2f2;
  color: #dc2626;
  border: 1px solid #fecaca;
}

/* 可点击状态 */
.tag-clickable {
  cursor: pointer;
}

.tag-clickable:active {
  transform: scale(0.95);
}

.tag-clickable.tag-default:active {
  background: #e2e8f0;
}

.tag-clickable.tag-primary:active {
  opacity: 0.9;
}

.tag-clickable.tag-success:active {
  background: #bbf7d0;
}

.tag-clickable.tag-warning:active {
  background: #fed7aa;
}

.tag-clickable.tag-danger:active {
  background: #fecaca;
}

/* 技能名称 */
.skill-name {
  flex: 1;
}

/* 技能等级 */
.skill-level {
  display: flex;
  align-items: center;
  margin-left: 8rpx;
}

.star {
  font-size: 16rpx;
  margin-right: 2rpx;
}

.star:last-child {
  margin-right: 0;
}

.star-filled {
  opacity: 1;
}

.star-empty {
  opacity: 0.3;
}

/* 删除按钮 */
.tag-closable {
  padding-right: 8rpx;
}

.close-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32rpx;
  height: 32rpx;
  margin-left: 8rpx;
  border-radius: 50%;
  background: rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
}

.close-btn:active {
  background: rgba(0, 0, 0, 0.2);
  transform: scale(0.9);
}

.close-icon {
  font-size: 24rpx;
  color: #64748b;
  line-height: 1;
}

/* 不同类型的删除按钮 */
.tag-primary .close-btn {
  background: rgba(255, 255, 255, 0.2);
}

.tag-primary .close-icon {
  color: #ffffff;
}

.tag-success .close-btn {
  background: rgba(22, 163, 74, 0.1);
}

.tag-success .close-icon {
  color: #16a34a;
}

.tag-warning .close-btn {
  background: rgba(217, 119, 6, 0.1);
}

.tag-warning .close-icon {
  color: #d97706;
}

.tag-danger .close-btn {
  background: rgba(220, 38, 38, 0.1);
}

.tag-danger .close-icon {
  color: #dc2626;
}

/* 响应式适配 */
@media (max-width: 750rpx) {
  .tag-large {
    padding: 12rpx 20rpx;
    font-size: 24rpx;
  }
  
  .tag-medium {
    padding: 10rpx 16rpx;
    font-size: 22rpx;
  }
  
  .tag-small {
    padding: 6rpx 12rpx;
    font-size: 18rpx;
  }
}
