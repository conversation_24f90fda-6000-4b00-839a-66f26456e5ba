// components/resume/skill-tag/index.js
Component({
  /**
   * 组件的属性列表
   */
  properties: {
    // 技能名称
    skill: {
      type: String,
      value: ''
    },
    // 技能等级 (1-5)
    level: {
      type: Number,
      value: 0
    },
    // 标签类型：default | primary | success | warning | danger
    type: {
      type: String,
      value: 'default'
    },
    // 标签大小：small | medium | large
    size: {
      type: String,
      value: 'medium'
    },
    // 是否可删除
    closable: {
      type: Boolean,
      value: false
    },
    // 是否可点击
    clickable: {
      type: Boolean,
      value: false
    },
    // 自定义样式类
    customClass: {
      type: String,
      value: ''
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    // 技能等级对应的星星数组
    stars: []
  },

  /**
   * 组件生命周期
   */
  lifetimes: {
    attached() {
      this.updateStars()
    }
  },

  /**
   * 监听属性变化
   */
  observers: {
    'level': function(newLevel) {
      this.updateStars()
    }
  },

  /**
   * 组件的方法列表
   */
  methods: {
    /**
     * 更新星星显示
     */
    updateStars() {
      const level = this.data.level
      const stars = []
      
      for (let i = 1; i <= 5; i++) {
        stars.push({
          filled: i <= level,
          index: i
        })
      }
      
      this.setData({
        stars: stars
      })
    },

    /**
     * 点击标签
     */
    onTap() {
      if (this.data.clickable) {
        this.triggerEvent('tap', {
          skill: this.data.skill,
          level: this.data.level
        })
      }
    },

    /**
     * 删除标签
     */
    onClose(e) {
      e.stopPropagation()
      this.triggerEvent('close', {
        skill: this.data.skill,
        level: this.data.level
      })
    },

    /**
     * 获取标签样式类
     */
    getTagClass() {
      const { type, size, clickable, closable } = this.data
      let classes = ['skill-tag']
      
      classes.push(`tag-${type}`)
      classes.push(`tag-${size}`)
      
      if (clickable) {
        classes.push('tag-clickable')
      }
      
      if (closable) {
        classes.push('tag-closable')
      }
      
      return classes.join(' ')
    }
  }
})
