// components/resume/template-selector/index.js
Component({
  /**
   * 组件的属性列表
   */
  properties: {
    // 模板列表
    templates: {
      type: Array,
      value: []
    },
    // 当前选中的模板ID
    selectedId: {
      type: String,
      value: ''
    },
    // 显示模式：grid | list
    mode: {
      type: String,
      value: 'grid'
    },
    // 是否显示预览按钮
    showPreview: {
      type: Boolean,
      value: true
    },
    // 自定义样式类
    customClass: {
      type: String,
      value: ''
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    // 默认模板列表
    defaultTemplates: [
      {
        id: 'modern',
        name: '现代简约',
        preview: '/imgs/template-modern.png',
        description: '简洁现代，适合互联网行业',
        tags: ['简约', '现代', '互联网'],
        color: '#0ea5e9'
      },
      {
        id: 'classic',
        name: '经典商务',
        preview: '/imgs/template-classic.png',
        description: '传统正式，适合传统行业',
        tags: ['正式', '传统', '商务'],
        color: '#1e293b'
      },
      {
        id: 'creative',
        name: '创意设计',
        preview: '/imgs/template-creative.png',
        description: '富有创意，适合设计行业',
        tags: ['创意', '设计', '艺术'],
        color: '#8b5cf6'
      },
      {
        id: 'minimal',
        name: '极简风格',
        preview: '/imgs/template-minimal.png',
        description: '极简设计，突出内容',
        tags: ['极简', '清爽', '专业'],
        color: '#22c55e'
      }
    ]
  },

  /**
   * 组件生命周期
   */
  lifetimes: {
    attached() {
      // 如果没有传入模板列表，使用默认模板
      if (!this.data.templates || this.data.templates.length === 0) {
        this.setData({
          templates: this.data.defaultTemplates
        })
      }
    }
  },

  /**
   * 组件的方法列表
   */
  methods: {
    /**
     * 选择模板
     */
    onSelectTemplate(e) {
      const { templateId } = e.currentTarget.dataset
      const template = this.getTemplateById(templateId)
      
      this.setData({
        selectedId: templateId
      })
      
      this.triggerEvent('select', {
        templateId: templateId,
        template: template
      })
    },

    /**
     * 预览模板
     */
    onPreviewTemplate(e) {
      e.stopPropagation()
      const { templateId } = e.currentTarget.dataset
      const template = this.getTemplateById(templateId)
      
      this.triggerEvent('preview', {
        templateId: templateId,
        template: template
      })
    },

    /**
     * 根据ID获取模板
     */
    getTemplateById(templateId) {
      const templates = this.data.templates.length > 0 ? this.data.templates : this.data.defaultTemplates
      return templates.find(template => template.id === templateId)
    },

    /**
     * 检查是否为选中状态
     */
    isSelected(templateId) {
      return this.data.selectedId === templateId
    },

    /**
     * 获取模板卡片样式类
     */
    getCardClass(templateId) {
      let classes = ['template-card']
      
      if (this.isSelected(templateId)) {
        classes.push('selected')
      }
      
      if (this.data.mode === 'list') {
        classes.push('list-mode')
      }
      
      return classes.join(' ')
    }
  }
})
