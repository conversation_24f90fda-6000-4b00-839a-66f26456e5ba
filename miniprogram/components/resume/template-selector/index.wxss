/* components/resume/template-selector/index.wxss */

.template-selector {
  width: 100%;
}

/* 网格模式 */
.grid-container {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 24rpx;
  padding: 24rpx;
}

.template-card {
  background: #ffffff;
  border-radius: 24rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  border: 2px solid transparent;
}

.template-card:active {
  transform: scale(0.98);
}

.template-card.selected {
  border-color: #0ea5e9;
  box-shadow: 0 8rpx 24rpx rgba(14, 165, 233, 0.3);
}

/* 模板预览图 */
.template-preview {
  position: relative;
  height: 300rpx;
  background: #f8fafc;
  overflow: hidden;
}

.preview-image {
  width: 100%;
  height: 100%;
}

/* 选中状态指示器 */
.selected-indicator {
  position: absolute;
  top: 16rpx;
  right: 16rpx;
  width: 48rpx;
  height: 48rpx;
  background: #0ea5e9;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.check-icon {
  color: #ffffff;
  font-size: 24rpx;
  font-weight: bold;
}

/* 预览按钮 */
.preview-btn {
  position: absolute;
  bottom: 16rpx;
  right: 16rpx;
  width: 48rpx;
  height: 48rpx;
  background: rgba(0, 0, 0, 0.6);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.preview-btn:active {
  background: rgba(0, 0, 0, 0.8);
  transform: scale(0.9);
}

.preview-icon {
  color: #ffffff;
  font-size: 20rpx;
}

/* 模板信息 */
.template-info {
  padding: 24rpx;
}

.template-name {
  font-size: 28rpx;
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 8rpx;
}

.template-description {
  font-size: 24rpx;
  color: #64748b;
  line-height: 1.4;
  margin-bottom: 16rpx;
}

/* 标签 */
.template-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8rpx;
}

.tag {
  padding: 6rpx 12rpx;
  border: 1px solid #e2e8f0;
  border-radius: 16rpx;
  font-size: 20rpx;
  background: #ffffff;
}

/* 列表模式 */
.list-container {
  padding: 24rpx;
}

.list-mode .template-card {
  display: flex;
  align-items: center;
  padding: 24rpx;
  margin-bottom: 16rpx;
  border-radius: 16rpx;
}

.list-mode .template-card:last-child {
  margin-bottom: 0;
}

/* 列表预览图 */
.list-preview {
  width: 120rpx;
  height: 160rpx;
  background: #f8fafc;
  border-radius: 12rpx;
  overflow: hidden;
  margin-right: 24rpx;
  flex-shrink: 0;
}

.list-preview-image {
  width: 100%;
  height: 100%;
}

/* 列表信息 */
.list-info {
  flex: 1;
  margin-right: 16rpx;
}

.list-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 8rpx;
}

.list-name {
  font-size: 32rpx;
  font-weight: 600;
  color: #1e293b;
}

.list-selected {
  width: 40rpx;
  height: 40rpx;
  background: #0ea5e9;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.list-check-icon {
  color: #ffffff;
  font-size: 20rpx;
  font-weight: bold;
}

.list-description {
  font-size: 24rpx;
  color: #64748b;
  line-height: 1.4;
  margin-bottom: 12rpx;
}

/* 列表标签 */
.list-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8rpx;
}

.list-tag {
  padding: 4rpx 8rpx;
  background: #f1f5f9;
  border-radius: 12rpx;
  font-size: 20rpx;
  color: #475569;
}

/* 列表预览按钮 */
.list-preview-btn {
  width: 64rpx;
  height: 64rpx;
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.list-preview-btn:active {
  background: #e2e8f0;
  transform: scale(0.9);
}

.list-preview-icon {
  font-size: 24rpx;
  color: #475569;
}

/* 响应式适配 */
@media (max-width: 750rpx) {
  .grid-container {
    grid-template-columns: 1fr;
    gap: 16rpx;
    padding: 16rpx;
  }
  
  .template-preview {
    height: 240rpx;
  }
  
  .list-mode .template-card {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .list-preview {
    width: 100%;
    height: 200rpx;
    margin-right: 0;
    margin-bottom: 16rpx;
  }
  
  .list-info {
    margin-right: 0;
    width: 100%;
  }
  
  .list-preview-btn {
    align-self: flex-end;
    margin-top: 16rpx;
  }
}
