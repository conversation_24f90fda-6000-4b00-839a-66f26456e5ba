<!--components/resume/template-selector/index.wxml-->
<view class="template-selector {{customClass}} {{mode}}-mode">
  <!-- 网格模式 -->
  <view wx:if="{{mode === 'grid'}}" class="grid-container">
    <view 
      class="{{getCardClass(template.id)}}"
      wx:for="{{templates.length > 0 ? templates : defaultTemplates}}" 
      wx:key="id"
      wx:for-item="template"
      data-template-id="{{template.id}}"
      bindtap="onSelectTemplate"
    >
      <!-- 模板预览图 -->
      <view class="template-preview">
        <image 
          class="preview-image" 
          src="{{template.preview}}" 
          mode="aspectFit"
          lazy-load="{{true}}"
        />
        
        <!-- 选中状态指示器 -->
        <view wx:if="{{isSelected(template.id)}}" class="selected-indicator">
          <text class="check-icon">✓</text>
        </view>
        
        <!-- 预览按钮 -->
        <view wx:if="{{showPreview}}" class="preview-btn" data-template-id="{{template.id}}" bindtap="onPreviewTemplate">
          <text class="preview-icon">👁</text>
        </view>
      </view>
      
      <!-- 模板信息 -->
      <view class="template-info">
        <view class="template-name">{{template.name}}</view>
        <view class="template-description">{{template.description}}</view>
        
        <!-- 标签 -->
        <view wx:if="{{template.tags && template.tags.length > 0}}" class="template-tags">
          <view 
            class="tag"
            wx:for="{{template.tags}}" 
            wx:key="index"
            wx:for-item="tag"
            style="border-color: {{template.color}}; color: {{template.color}}"
          >
            {{tag}}
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 列表模式 -->
  <view wx:else class="list-container">
    <view 
      class="{{getCardClass(template.id)}}"
      wx:for="{{templates.length > 0 ? templates : defaultTemplates}}" 
      wx:key="id"
      wx:for-item="template"
      data-template-id="{{template.id}}"
      bindtap="onSelectTemplate"
    >
      <!-- 模板预览图 -->
      <view class="list-preview">
        <image 
          class="list-preview-image" 
          src="{{template.preview}}" 
          mode="aspectFit"
          lazy-load="{{true}}"
        />
      </view>
      
      <!-- 模板信息 -->
      <view class="list-info">
        <view class="list-header">
          <view class="list-name">{{template.name}}</view>
          <view wx:if="{{isSelected(template.id)}}" class="list-selected">
            <text class="list-check-icon">✓</text>
          </view>
        </view>
        
        <view class="list-description">{{template.description}}</view>
        
        <!-- 标签 -->
        <view wx:if="{{template.tags && template.tags.length > 0}}" class="list-tags">
          <view 
            class="list-tag"
            wx:for="{{template.tags}}" 
            wx:key="index"
            wx:for-item="tag"
          >
            {{tag}}
          </view>
        </view>
      </view>
      
      <!-- 预览按钮 -->
      <view wx:if="{{showPreview}}" class="list-preview-btn" data-template-id="{{template.id}}" bindtap="onPreviewTemplate">
        <text class="list-preview-icon">👁</text>
      </view>
    </view>
  </view>
</view>
