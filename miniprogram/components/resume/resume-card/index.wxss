/* components/resume/resume-card/index.wxss */

.resume-card {
  background: #ffffff;
  border-radius: 24rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  overflow: hidden;
  margin-bottom: 32rpx;
}

/* 预览模式样式 */
.preview-card {
  padding: 32rpx;
}

.card-header {
  display: flex;
  align-items: flex-start;
  margin-bottom: 32rpx;
}

.avatar-section {
  margin-right: 24rpx;
}

.avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  background: #f1f5f9;
}

.basic-info {
  flex: 1;
}

.name {
  font-size: 36rpx;
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 8rpx;
}

.position {
  font-size: 28rpx;
  color: #0ea5e9;
  font-weight: 500;
  margin-bottom: 12rpx;
}

.meta-info {
  font-size: 24rpx;
  color: #64748b;
}

.separator {
  margin: 0 8rpx;
}

/* 教育背景 */
.education-section {
  margin-bottom: 24rpx;
}

.section-title {
  font-size: 24rpx;
  color: #64748b;
  margin-bottom: 8rpx;
}

.education-info {
  font-size: 28rpx;
  color: #1e293b;
}

/* 技能标签 */
.skills-section {
  margin-bottom: 24rpx;
}

.skills-list {
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;
}

.skill-tag {
  padding: 8rpx 16rpx;
  background: #f1f5f9;
  border: 1px solid #e2e8f0;
  border-radius: 32rpx;
  font-size: 24rpx;
  color: #475569;
}

/* 完整度指示器 */
.completeness-section {
  display: flex;
  align-items: center;
  margin-bottom: 32rpx;
  padding: 24rpx;
  background: #f8fafc;
  border-radius: 16rpx;
}

.completeness-label {
  font-size: 24rpx;
  color: #64748b;
  margin-right: 16rpx;
}

.completeness-bar {
  flex: 1;
  height: 8rpx;
  background: #e2e8f0;
  border-radius: 4rpx;
  overflow: hidden;
  margin-right: 16rpx;
}

.completeness-fill {
  height: 100%;
  background: linear-gradient(90deg, #22c55e, #16a34a);
  border-radius: 4rpx;
  transition: width 0.3s ease;
}

.completeness-text {
  font-size: 24rpx;
  font-weight: 600;
  color: #0ea5e9;
}

/* 操作按钮 */
.actions {
  display: flex;
  gap: 16rpx;
}

.action-btn {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 16rpx;
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 16rpx;
  font-size: 24rpx;
  color: #475569;
}

.action-btn:active {
  background: #e2e8f0;
  transform: scale(0.98);
}

.btn-icon {
  font-size: 32rpx;
  margin-bottom: 8rpx;
}

.btn-text {
  font-size: 20rpx;
}

/* 紧凑模式样式 */
.compact-card {
  padding: 24rpx;
}

.compact-header {
  display: flex;
  align-items: center;
}

.compact-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  margin-right: 16rpx;
  background: #f1f5f9;
}

.compact-info {
  flex: 1;
}

.compact-name {
  font-size: 28rpx;
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 4rpx;
}

.compact-position {
  font-size: 24rpx;
  color: #64748b;
}

.compact-completeness {
  text-align: right;
}

.completeness-number {
  font-size: 24rpx;
  font-weight: 600;
  color: #0ea5e9;
}

/* 编辑模式样式 */
.edit-card {
  padding: 32rpx;
}

.edit-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
}

.edit-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #1e293b;
}

.edit-status {
  padding: 8rpx 16rpx;
  background: #dcfce7;
  border-radius: 16rpx;
}

.status-text {
  font-size: 20rpx;
  color: #16a34a;
}

.edit-content {
  margin-bottom: 32rpx;
}

.edit-item {
  display: flex;
  align-items: center;
  padding: 16rpx 0;
  border-bottom: 1px solid #f1f5f9;
}

.edit-item:last-child {
  border-bottom: none;
}

.item-label {
  font-size: 24rpx;
  color: #64748b;
  width: 120rpx;
}

.item-value {
  font-size: 28rpx;
  color: #1e293b;
  flex: 1;
}

.edit-actions {
  display: flex;
  gap: 16rpx;
}

.edit-action-btn {
  flex: 1;
  padding: 20rpx;
  border-radius: 16rpx;
  font-size: 28rpx;
  text-align: center;
}

.continue-btn {
  background: linear-gradient(135deg, #0ea5e9, #3b82f6);
  color: #ffffff;
}

.delete-btn {
  background: #fef2f2;
  color: #dc2626;
  border: 1px solid #fecaca;
}

.edit-action-btn:active {
  transform: scale(0.98);
}

/* 响应式适配 */
@media (max-width: 750rpx) {
  .actions {
    flex-direction: column;
  }
  
  .action-btn {
    flex-direction: row;
    justify-content: center;
  }
  
  .btn-icon {
    margin-bottom: 0;
    margin-right: 8rpx;
  }
}
