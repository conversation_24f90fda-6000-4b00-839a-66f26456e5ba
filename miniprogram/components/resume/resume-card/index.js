// components/resume/resume-card/index.js
Component({
  /**
   * 组件的属性列表
   */
  properties: {
    // 简历数据
    resumeData: {
      type: Object,
      value: {}
    },
    // 卡片模式：preview | edit | compact
    mode: {
      type: String,
      value: 'preview'
    },
    // 是否显示操作按钮
    showActions: {
      type: Boolean,
      value: true
    },
    // 自定义样式类
    customClass: {
      type: String,
      value: ''
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    // 默认头像
    defaultAvatar: '/imgs/default-avatar.png'
  },

  /**
   * 组件的方法列表
   */
  methods: {
    /**
     * 预览简历
     */
    onPreview() {
      this.triggerEvent('preview', {
        resumeData: this.data.resumeData
      })
    },

    /**
     * 编辑简历
     */
    onEdit() {
      this.triggerEvent('edit', {
        resumeData: this.data.resumeData
      })
    },

    /**
     * 分享简历
     */
    onShare() {
      this.triggerEvent('share', {
        resumeData: this.data.resumeData
      })
    },

    /**
     * 删除简历
     */
    onDelete() {
      wx.showModal({
        title: '确认删除',
        content: '删除后无法恢复，确定要删除这份简历吗？',
        success: (res) => {
          if (res.confirm) {
            this.triggerEvent('delete', {
              resumeData: this.data.resumeData
            })
          }
        }
      })
    },

    /**
     * 格式化日期
     */
    formatDate(dateStr) {
      if (!dateStr) return ''
      const date = new Date(dateStr)
      return `${date.getFullYear()}.${String(date.getMonth() + 1).padStart(2, '0')}`
    },

    /**
     * 获取工作年限
     */
    getWorkYears() {
      const { workExperience } = this.data.resumeData
      if (!workExperience || workExperience.length === 0) return '应届生'
      
      let totalMonths = 0
      workExperience.forEach(exp => {
        if (exp.startDate && exp.endDate) {
          const start = new Date(exp.startDate)
          const end = new Date(exp.endDate)
          const months = (end.getFullYear() - start.getFullYear()) * 12 + 
                        (end.getMonth() - start.getMonth())
          totalMonths += months
        }
      })
      
      const years = Math.floor(totalMonths / 12)
      return years > 0 ? `${years}年经验` : '1年内经验'
    },

    /**
     * 获取最高学历
     */
    getHighestEducation() {
      const { education } = this.data.resumeData
      if (!education || education.length === 0) return ''
      
      const degreeOrder = {
        '博士': 4,
        '硕士': 3,
        '本科': 2,
        '专科': 1,
        '高中': 0
      }
      
      let highest = education[0]
      education.forEach(edu => {
        if (degreeOrder[edu.degree] > degreeOrder[highest.degree]) {
          highest = edu
        }
      })
      
      return `${highest.degree} · ${highest.school}`
    },

    /**
     * 获取核心技能
     */
    getCoreSkills() {
      const { skills } = this.data.resumeData
      if (!skills || skills.length === 0) return []
      return skills.slice(0, 3) // 只显示前3个技能
    }
  }
})
