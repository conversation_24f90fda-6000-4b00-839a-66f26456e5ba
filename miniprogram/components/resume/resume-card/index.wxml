<!--components/resume/resume-card/index.wxml-->
<view class="resume-card {{customClass}} {{mode}}-mode">
  <!-- 预览模式 -->
  <view wx:if="{{mode === 'preview'}}" class="preview-card">
    <!-- 头部信息 -->
    <view class="card-header">
      <view class="avatar-section">
        <image
          class="avatar"
          src="{{resumeData.personalInfo.avatar || defaultAvatar}}"
          mode="aspectFill"
        />
      </view>
      <view class="basic-info">
        <view class="name">{{resumeData.personalInfo.name || '未填写姓名'}}</view>
        <view class="position">{{resumeData.jobIntention.position || '求职意向未填写'}}</view>
        <view class="meta-info">
          <text class="work-years">{{getWorkYears()}}</text>
          <text class="separator">·</text>
          <text class="location">{{resumeData.personalInfo.location || '地点未填写'}}</text>
        </view>
      </view>
    </view>

    <!-- 教育背景 -->
    <view wx:if="{{resumeData.education && resumeData.education.length > 0}}" class="education-section">
      <view class="section-title">教育背景</view>
      <view class="education-info">{{getHighestEducation()}}</view>
    </view>

    <!-- 核心技能 -->
    <view wx:if="{{resumeData.skills && resumeData.skills.length > 0}}" class="skills-section">
      <view class="section-title">核心技能</view>
      <view class="skills-list">
        <view
          class="skill-tag"
          wx:for="{{getCoreSkills()}}"
          wx:key="index"
        >
          {{item}}
        </view>
      </view>
    </view>

    <!-- 完整度指示器 -->
    <view class="completeness-section">
      <view class="completeness-label">简历完整度</view>
      <view class="completeness-bar">
        <view class="completeness-fill" style="width: {{resumeData.completeness || 0}}%"></view>
      </view>
      <view class="completeness-text">{{resumeData.completeness || 0}}%</view>
    </view>

    <!-- 操作按钮 -->
    <view wx:if="{{showActions}}" class="actions">
      <button class="action-btn preview-btn" bindtap="onPreview">
        <text class="btn-icon">👁</text>
        <text class="btn-text">预览</text>
      </button>
      <button class="action-btn edit-btn" bindtap="onEdit">
        <text class="btn-icon">✏️</text>
        <text class="btn-text">编辑</text>
      </button>
      <button class="action-btn share-btn" bindtap="onShare">
        <text class="btn-icon">📤</text>
        <text class="btn-text">分享</text>
      </button>
    </view>
  </view>

  <!-- 紧凑模式 -->
  <view wx:elif="{{mode === 'compact'}}" class="compact-card">
    <view class="compact-header">
      <image
        class="compact-avatar"
        src="{{resumeData.personalInfo.avatar || defaultAvatar}}"
        mode="aspectFill"
      />
      <view class="compact-info">
        <view class="compact-name">{{resumeData.personalInfo.name || '未填写'}}</view>
        <view class="compact-position">{{resumeData.jobIntention.position || '求职意向'}}</view>
      </view>
      <view class="compact-completeness">
        <text class="completeness-number">{{resumeData.completeness || 0}}%</text>
      </view>
    </view>
  </view>

  <!-- 编辑模式 -->
  <view wx:else class="edit-card">
    <view class="edit-header">
      <view class="edit-title">编辑简历</view>
      <view class="edit-status">
        <text class="status-text">{{resumeData.completeness >= 80 ? '已完成' : '进行中'}}</text>
      </view>
    </view>

    <view class="edit-content">
      <view class="edit-item">
        <text class="item-label">姓名：</text>
        <text class="item-value">{{resumeData.personalInfo.name || '未填写'}}</text>
      </view>
      <view class="edit-item">
        <text class="item-label">职位：</text>
        <text class="item-value">{{resumeData.jobIntention.position || '未填写'}}</text>
      </view>
      <view class="edit-item">
        <text class="item-label">完整度：</text>
        <text class="item-value">{{resumeData.completeness || 0}}%</text>
      </view>
    </view>

    <view wx:if="{{showActions}}" class="edit-actions">
      <button class="edit-action-btn continue-btn" bindtap="onEdit">继续编辑</button>
      <button class="edit-action-btn delete-btn" bindtap="onDelete">删除</button>
    </view>
  </view>
</view>

