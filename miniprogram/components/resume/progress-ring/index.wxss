/* components/resume/progress-ring/index.wxss */

.progress-ring {
  position: relative;
  display: inline-block;
}

.progress-svg {
  transform: rotate(0deg);
  overflow: visible;
}

.progress-bg {
  opacity: 0.3;
}

.progress-bar {
  transition: stroke-dashoffset 0.3s ease;
  filter: drop-shadow(0 0 4rpx rgba(14, 165, 233, 0.3));
}

.progress-text {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  align-items: baseline;
  justify-content: center;
  font-weight: 600;
}

.progress-number {
  font-size: 24rpx;
  color: #1e293b;
  line-height: 1;
}

.progress-percent {
  font-size: 16rpx;
  color: #64748b;
  margin-left: 2rpx;
}
