// components/resume/progress-ring/index.js
Component({
  /**
   * 组件的属性列表
   */
  properties: {
    // 进度值 (0-100)
    progress: {
      type: Number,
      value: 0
    },
    // 环形大小
    size: {
      type: Number,
      value: 80
    },
    // 线条宽度
    strokeWidth: {
      type: Number,
      value: 6
    },
    // 进度条颜色
    color: {
      type: String,
      value: '#0ea5e9'
    },
    // 背景色
    backgroundColor: {
      type: String,
      value: '#e2e8f0'
    },
    // 是否显示百分比文字
    showText: {
      type: Boolean,
      value: true
    },
    // 动画持续时间(ms)
    duration: {
      type: Number,
      value: 1000
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    // SVG相关计算属性
    radius: 0,
    circumference: 0,
    strokeDasharray: '',
    strokeDashoffset: 0,
    // 动画相关
    animatedProgress: 0,
    isAnimating: false
  },

  /**
   * 组件生命周期
   */
  lifetimes: {
    attached() {
      this.calculateSVGProperties()
      this.animateProgress()
    }
  },

  /**
   * 监听属性变化
   */
  observers: {
    'progress': function(newProgress) {
      if (!this.data.isAnimating) {
        this.animateProgress()
      }
    },
    'size, strokeWidth': function() {
      this.calculateSVGProperties()
    }
  },

  /**
   * 组件的方法列表
   */
  methods: {
    /**
     * 计算SVG相关属性
     */
    calculateSVGProperties() {
      const { size, strokeWidth } = this.data
      const radius = (size - strokeWidth) / 2
      const circumference = 2 * Math.PI * radius
      
      this.setData({
        radius: radius,
        circumference: circumference,
        strokeDasharray: `${circumference} ${circumference}`
      })
    },

    /**
     * 动画进度条
     */
    animateProgress() {
      const { progress, duration, circumference } = this.data
      const targetProgress = Math.max(0, Math.min(100, progress))
      
      this.setData({
        isAnimating: true
      })

      // 使用requestAnimationFrame实现平滑动画
      const startTime = Date.now()
      const startProgress = this.data.animatedProgress
      const progressDiff = targetProgress - startProgress

      const animate = () => {
        const elapsed = Date.now() - startTime
        const progressRatio = Math.min(elapsed / duration, 1)
        
        // 使用easeOutCubic缓动函数
        const easeOutCubic = 1 - Math.pow(1 - progressRatio, 3)
        const currentProgress = startProgress + (progressDiff * easeOutCubic)
        
        // 计算stroke-dashoffset
        const offset = circumference - (currentProgress / 100) * circumference
        
        this.setData({
          animatedProgress: currentProgress,
          strokeDashoffset: offset
        })

        if (progressRatio < 1) {
          // 继续动画
          setTimeout(animate, 16) // 约60fps
        } else {
          // 动画完成
          this.setData({
            isAnimating: false,
            animatedProgress: targetProgress,
            strokeDashoffset: circumference - (targetProgress / 100) * circumference
          })
        }
      }

      animate()
    },

    /**
     * 重置进度
     */
    reset() {
      this.setData({
        animatedProgress: 0,
        strokeDashoffset: this.data.circumference
      })
    },

    /**
     * 设置进度
     */
    setProgress(progress) {
      this.setData({
        progress: progress
      })
    }
  }
})
