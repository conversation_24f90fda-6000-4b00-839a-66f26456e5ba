<!--components/resume/progress-ring/index.wxml-->
<view class="progress-ring" style="width: {{size}}rpx; height: {{size}}rpx;">
  <!-- SVG环形进度条 -->
  <svg 
    class="progress-svg" 
    width="{{size}}rpx" 
    height="{{size}}rpx"
    viewBox="0 0 {{size}} {{size}}"
  >
    <!-- 背景圆环 -->
    <circle
      class="progress-bg"
      cx="{{size / 2}}"
      cy="{{size / 2}}"
      r="{{radius}}"
      stroke="{{backgroundColor}}"
      stroke-width="{{strokeWidth}}"
      fill="transparent"
    />
    
    <!-- 进度圆环 -->
    <circle
      class="progress-bar"
      cx="{{size / 2}}"
      cy="{{size / 2}}"
      r="{{radius}}"
      stroke="{{color}}"
      stroke-width="{{strokeWidth}}"
      fill="transparent"
      stroke-dasharray="{{strokeDasharray}}"
      stroke-dashoffset="{{strokeDashoffset}}"
      stroke-linecap="round"
      transform="rotate(-90 {{size / 2}} {{size / 2}})"
    />
  </svg>
  
  <!-- 中心文字 -->
  <view wx:if="{{showText}}" class="progress-text">
    <text class="progress-number">{{Math.round(animatedProgress)}}</text>
    <text class="progress-percent">%</text>
  </view>
</view>
