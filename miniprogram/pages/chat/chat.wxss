/* pages/chat/chat.wxss */

/* 页面容器 */
.chat-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
}

/* 顶部进度区域 */
.progress-header {
  display: flex;
  align-items: center;
  padding: 32rpx;
  background: #ffffff;
  border-bottom: 1px solid #e2e8f0;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.progress-info {
  flex: 1;
  margin-left: 24rpx;
}

.stage-title {
  display: block;
  font-size: 32rpx;
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 8rpx;
}

.stage-desc {
  display: block;
  font-size: 24rpx;
  color: #64748b;
}

.preview-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 16rpx;
  background: #f1f5f9;
  border-radius: 16rpx;
  min-width: 80rpx;
}

.preview-btn text {
  font-size: 20rpx;
  color: #475569;
  margin-top: 4rpx;
}

/* 消息容器 */
.messages-container {
  flex: 1;
  padding: 0 32rpx;
  overflow-y: auto;
}

.messages-list {
  padding: 32rpx 0;
}

/* 消息项 */
.message-item {
  margin-bottom: 32rpx;
}

.ai-message {
  display: flex;
  justify-content: flex-start;
}

.user-message {
  display: flex;
  justify-content: flex-end;
}

/* AI消息气泡 */
.ai-bubble {
  display: flex;
  align-items: flex-end;
  max-width: 80%;
}

.avatar {
  width: 64rpx;
  height: 64rpx;
  border-radius: 50%;
  background: linear-gradient(135deg, #0ea5e9, #3b82f6);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16rpx;
  flex-shrink: 0;
}

.avatar-img {
  width: 40rpx;
  height: 40rpx;
  border-radius: 50%;
}

.ai-content {
  background: #ffffff;
  border-radius: 32rpx 32rpx 32rpx 8rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

/* 用户消息气泡 */
.user-bubble {
  display: flex;
  justify-content: flex-end;
  max-width: 80%;
}

.user-content {
  background: linear-gradient(135deg, #0ea5e9, #3b82f6);
  color: #ffffff;
  border-radius: 32rpx 32rpx 8rpx 32rpx;
  box-shadow: 0 4rpx 12rpx rgba(14, 165, 233, 0.3);
}

/* 气泡内容 */
.bubble-content {
  padding: 24rpx 32rpx;
  position: relative;
}

.message-text {
  display: block;
  font-size: 28rpx;
  line-height: 1.6;
  word-wrap: break-word;
}

.message-time {
  display: block;
  font-size: 20rpx;
  opacity: 0.6;
  margin-top: 8rpx;
}

/* 输入状态指示器 */
.typing-indicator {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 8rpx 0;
}

.typing-dot {
  width: 12rpx;
  height: 12rpx;
  border-radius: 50%;
  background: #94a3b8;
  animation: typing-pulse 1.4s ease-in-out infinite;
}

.typing-dot:nth-child(2) {
  animation-delay: 0.2s;
}

.typing-dot:nth-child(3) {
  animation-delay: 0.4s;
}

@keyframes typing-pulse {
  0%, 60%, 100% {
    opacity: 0.4;
    transform: scale(1);
  }
  30% {
    opacity: 1;
    transform: scale(1.2);
  }
}

/* 快捷回复 */
.quick-replies {
  padding: 24rpx 32rpx;
  background: #ffffff;
  border-top: 1px solid #e2e8f0;
}

.quick-replies-title {
  font-size: 24rpx;
  color: #64748b;
  margin-bottom: 16rpx;
}

.quick-replies-list {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
}

.quick-reply-item {
  padding: 16rpx 24rpx;
  background: #f1f5f9;
  border: 1px solid #e2e8f0;
  border-radius: 32rpx;
  font-size: 24rpx;
  color: #475569;
  transition: all 0.2s ease;
}

.quick-reply-item:active {
  background: #e2e8f0;
  transform: scale(0.98);
}

/* 完整度卡片 */
.completeness-card {
  margin: 24rpx 32rpx;
  padding: 32rpx;
  background: #ffffff;
  border-radius: 24rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.card-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #1e293b;
}

.completeness-score {
  font-size: 32rpx;
  font-weight: 700;
  color: #0ea5e9;
}

.completeness-bar {
  height: 8rpx;
  background: #e2e8f0;
  border-radius: 4rpx;
  overflow: hidden;
  margin-bottom: 16rpx;
}

.completeness-fill {
  height: 100%;
  background: linear-gradient(90deg, #22c55e, #16a34a);
  border-radius: 4rpx;
  transition: width 0.3s ease;
}

.completeness-tips {
  text-align: center;
}

.tip-text {
  font-size: 24rpx;
  color: #64748b;
}

/* 底部输入区域 */
.input-area {
  padding: 24rpx 32rpx;
  background: #ffffff;
  border-top: 1px solid #e2e8f0;
  box-shadow: 0 -2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.input-container {
  display: flex;
  align-items: center;
  background: #f8fafc;
  border-radius: 32rpx;
  padding: 8rpx;
}

.message-input {
  flex: 1;
  padding: 16rpx 24rpx;
  font-size: 28rpx;
  background: transparent;
  border: none;
  outline: none;
}

.send-btn {
  width: 64rpx;
  height: 64rpx;
  border-radius: 50%;
  background: #e2e8f0;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.send-btn.active {
  background: linear-gradient(135deg, #0ea5e9, #3b82f6);
  color: #ffffff;
}

.send-btn:active {
  transform: scale(0.95);
}

/* 图标字体 */
.iconfont {
  font-family: 'iconfont';
  font-size: 32rpx;
}

/* 响应式适配 */
@media (max-width: 750rpx) {
  .progress-header {
    padding: 24rpx;
  }
  
  .messages-container {
    padding: 0 24rpx;
  }
  
  .input-area {
    padding: 16rpx 24rpx;
  }
}
