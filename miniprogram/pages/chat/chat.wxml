<!--pages/chat/chat.wxml-->
<view class="chat-container">
  <!-- 顶部进度条 -->
  <view class="progress-header">
    <progress-ring 
      progress="{{progress}}" 
      size="60" 
      stroke-width="4"
      color="#0ea5e9"
    />
    <view class="progress-info">
      <text class="stage-title">{{conversationStages[currentStage].title}}</text>
      <text class="stage-desc">{{conversationStages[currentStage].description}}</text>
    </view>
    <view class="preview-btn" bindtap="onPreviewResume">
      <text class="iconfont icon-preview"></text>
      <text>预览</text>
    </view>
  </view>

  <!-- 消息列表 -->
  <scroll-view 
    class="messages-container" 
    id="messages-container"
    scroll-y="{{true}}"
    scroll-top="{{scrollTop}}"
    scroll-with-animation="{{true}}"
  >
    <view class="messages-list">
      <!-- 消息气泡 -->
      <view 
        class="message-item {{message.isAI ? 'ai-message' : 'user-message'}}"
        wx:for="{{messages}}" 
        wx:key="id"
      >
        <!-- AI消息 -->
        <view wx:if="{{message.isAI}}" class="ai-bubble">
          <view class="avatar">
            <image src="/imgs/ai-avatar.png" class="avatar-img" />
          </view>
          <view class="bubble-content ai-content">
            <text class="message-text">{{message.content}}</text>
            <text class="message-time">{{message.timestamp}}</text>
          </view>
        </view>
        
        <!-- 用户消息 -->
        <view wx:else class="user-bubble">
          <view class="bubble-content user-content">
            <text class="message-text">{{message.content}}</text>
            <text class="message-time">{{message.timestamp}}</text>
          </view>
        </view>
      </view>
      
      <!-- 输入状态指示器 -->
      <view wx:if="{{isTyping}}" class="message-item ai-message">
        <view class="ai-bubble">
          <view class="avatar">
            <image src="/imgs/ai-avatar.png" class="avatar-img" />
          </view>
          <view class="bubble-content ai-content">
            <view class="typing-indicator">
              <view class="typing-dot"></view>
              <view class="typing-dot"></view>
              <view class="typing-dot"></view>
            </view>
          </view>
        </view>
      </view>
    </view>
  </scroll-view>

  <!-- 快捷回复 -->
  <view wx:if="{{showQuickReplies && quickReplies.length > 0}}" class="quick-replies">
    <view class="quick-replies-title">快速回复：</view>
    <view class="quick-replies-list">
      <view 
        class="quick-reply-item"
        wx:for="{{quickReplies}}" 
        wx:key="index"
        data-reply="{{item}}"
        bindtap="onQuickReply"
      >
        {{item}}
      </view>
    </view>
  </view>

  <!-- 简历完整度卡片 -->
  <view wx:if="{{resumeData.completeness > 0}}" class="completeness-card">
    <view class="card-header">
      <text class="card-title">简历完整度</text>
      <text class="completeness-score">{{resumeData.completeness}}%</text>
    </view>
    <view class="completeness-bar">
      <view 
        class="completeness-fill" 
        style="width: {{resumeData.completeness}}%"
      ></view>
    </view>
    <view class="completeness-tips">
      <text wx:if="{{resumeData.completeness < 60}}" class="tip-text">
        继续聊天，让简历更完整～
      </text>
      <text wx:elif="{{resumeData.completeness < 90}}" class="tip-text">
        简历已经很不错了，再补充一些细节会更好！
      </text>
      <text wx:else class="tip-text">
        🎉 简历信息很完整，可以生成简历了！
      </text>
    </view>
  </view>

  <!-- 底部输入区域 -->
  <view class="input-area">
    <view class="input-container">
      <input 
        class="message-input"
        type="text"
        placeholder="输入你想说的话..."
        value="{{inputValue}}"
        bindinput="onInput"
        confirm-type="send"
        bindconfirm="onSend"
      />
      <view 
        class="send-btn {{inputValue ? 'active' : ''}}"
        bindtap="onSend"
      >
        <text class="iconfont icon-send"></text>
      </view>
    </view>
  </view>
</view>
