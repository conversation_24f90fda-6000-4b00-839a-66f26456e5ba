// pages/chat/chat.js
Page({
  /**
   * 页面的初始数据
   */
  data: {
    // 对话相关数据
    messages: [],
    inputValue: '',
    isTyping: false,
    currentStage: 'greeting',
    conversationId: null,
    
    // 简历数据
    resumeData: {
      personalInfo: {},
      jobIntention: {},
      education: [],
      workExperience: [],
      projects: [],
      skills: [],
      personalTraits: [],
      completeness: 0
    },
    
    // UI状态
    showQuickReplies: false,
    quickReplies: [],
    progress: 0,
    
    // 对话阶段配置
    conversationStages: {
      greeting: {
        title: '初次见面',
        description: '建立信任，了解用户背景',
        progress: 10
      },
      intention: {
        title: '求职意向',
        description: '了解求职目标和期望',
        progress: 20
      },
      basicInfo: {
        title: '基本信息',
        description: '收集个人基本信息',
        progress: 30
      },
      education: {
        title: '教育背景',
        description: '了解教育经历和成就',
        progress: 50
      },
      experience: {
        title: '工作经历',
        description: '挖掘工作经验和亮点',
        progress: 70
      },
      projects: {
        title: '项目经验',
        description: '探索项目经历和技能',
        progress: 85
      },
      completion: {
        title: '完成总结',
        description: '整理信息，生成简历',
        progress: 100
      }
    }
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.initializeChat()
  },

  /**
   * 初始化对话
   */
  initializeChat() {
    const welcomeMessage = {
      id: Date.now(),
      content: '嗨！我是小简，你的专属简历顾问 ✨\n\n不用担心不知道怎么写简历，我们就像朋友聊天一样。我会帮你发现自己的亮点，讲出属于你的职业故事。\n\n准备好了吗？先从你最近在找什么样的工作开始聊吧～',
      isAI: true,
      timestamp: Date.now()
    }
    
    this.setData({
      messages: [welcomeMessage],
      quickReplies: ['我是应届毕业生', '我想要跳槽', '我正在转行'],
      showQuickReplies: true,
      progress: this.data.conversationStages.greeting.progress
    })
    
    this.createConversation()
  },

  /**
   * 创建对话记录
   */
  async createConversation() {
    try {
      // 暂时使用本地生成的ID，后续替换为云函数
      const conversationId = 'conv_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9)

      this.setData({
        conversationId: conversationId
      })

      console.log('对话创建成功:', conversationId)
    } catch (error) {
      console.error('创建对话失败:', error)
      wx.showToast({
        title: '初始化失败，请重试',
        icon: 'none'
      })
    }
  },

  /**
   * 发送消息
   */
  async sendMessage(content) {
    if (!content && !this.data.inputValue.trim()) {
      return
    }
    
    const messageContent = content || this.data.inputValue.trim()
    
    // 添加用户消息
    const userMessage = {
      id: Date.now(),
      content: messageContent,
      isAI: false,
      timestamp: Date.now()
    }
    
    this.setData({
      messages: [...this.data.messages, userMessage],
      inputValue: '',
      isTyping: true,
      showQuickReplies: false
    })
    
    // 滚动到底部
    this.scrollToBottom()
    
    try {
      // 暂时使用模拟AI回复，后续替换为真实AI调用
      const mockAIResponse = this.generateMockAIResponse(messageContent, this.data.currentStage)

      // 模拟AI思考时间
      setTimeout(() => {
        const aiMessage = {
          id: Date.now(),
          content: mockAIResponse.content,
          isAI: true,
          timestamp: Date.now()
        }

        // 更新简历数据
        const updatedResumeData = this.mergeResumeData(this.data.resumeData, mockAIResponse.extractedData)
        const completeness = this.calculateCompleteness(updatedResumeData)

        this.setData({
          messages: [...this.data.messages, aiMessage],
          isTyping: false,
          currentStage: mockAIResponse.nextStage,
          resumeData: updatedResumeData,
          quickReplies: mockAIResponse.suggestions || [],
          showQuickReplies: mockAIResponse.suggestions && mockAIResponse.suggestions.length > 0,
          progress: this.data.conversationStages[mockAIResponse.nextStage]?.progress || this.data.progress,
          'resumeData.completeness': completeness
        })

        this.scrollToBottom()
      }, 1500)

    } catch (error) {
      console.error('发送消息失败:', error)
      this.handleError('网络连接失败，请检查网络后重试')
    }
  },

  /**
   * 生成模拟AI回复（临时使用，后续替换为真实AI）
   */
  generateMockAIResponse(userMessage, currentStage) {
    const responses = {
      greeting: {
        content: '很高兴认识你！我是小简，你的专属简历顾问。我会帮你挖掘职业亮点，制作出色的简历。\n\n先告诉我，你目前在找什么样的工作呢？比如想应聘什么职位？',
        nextStage: 'intention',
        suggestions: ['前端开发工程师', '产品经理', '数据分析师', '其他职位'],
        extractedData: null
      },
      intention: {
        content: `好的，我了解你想找${userMessage}相关的工作。这是一个很有前景的方向！\n\n接下来，能告诉我你的基本信息吗？比如你的姓名、联系方式、所在城市等。`,
        nextStage: 'basicInfo',
        suggestions: ['我叫张三', '我在北京', '我的手机是...'],
        extractedData: {
          jobIntention: {
            position: userMessage
          }
        }
      },
      basicInfo: {
        content: '谢谢你提供的基本信息！这些信息很重要。\n\n现在让我们聊聊你的教育背景吧。你是从哪所学校毕业的？学的什么专业？有什么值得骄傲的成绩或经历吗？',
        nextStage: 'education',
        suggestions: ['本科毕业', '研究生学历', '专科学历'],
        extractedData: {
          personalInfo: {
            name: this.extractNameFromMessage(userMessage)
          }
        }
      },
      education: {
        content: '你的教育背景很不错！学习经历是简历的重要组成部分。\n\n接下来，我们聊聊你的工作经验吧。你之前做过什么工作？在哪些公司？主要负责什么？',
        nextStage: 'experience',
        suggestions: ['刚毕业没工作经验', '有1-3年经验', '有3年以上经验'],
        extractedData: {
          education: [{
            school: '示例大学',
            major: '计算机科学',
            degree: '本科'
          }]
        }
      },
      experience: {
        content: '你的工作经历很有价值！每一段经历都是成长的证明。\n\n最后，告诉我你掌握了哪些技能？比如编程语言、工具软件、或者其他专业技能？',
        nextStage: 'skills',
        suggestions: ['JavaScript', 'Python', 'Excel', '沟通能力'],
        extractedData: {
          workExperience: [{
            company: '示例公司',
            position: '示例职位',
            responsibilities: ['负责项目开发', '团队协作']
          }]
        }
      },
      skills: {
        content: '太棒了！你的技能很全面。\n\n现在我已经收集了足够的信息来为你制作简历。让我整理一下，你可以预览和调整内容。',
        nextStage: 'completion',
        suggestions: [],
        extractedData: {
          skills: ['JavaScript', 'Python', '项目管理']
        }
      },
      completion: {
        content: '🎉 恭喜！你的简历信息已经收集完成。\n\n你可以点击预览按钮查看简历，或者继续完善更多细节。',
        nextStage: 'completion',
        suggestions: ['预览简历', '继续完善', '选择模板'],
        extractedData: null
      }
    }

    return responses[currentStage] || responses.greeting
  },

  /**
   * 从消息中提取姓名（简单实现）
   */
  extractNameFromMessage(message) {
    const namePatterns = [
      /我叫(.+)/,
      /我是(.+)/,
      /姓名[：:](.+)/,
      /名字[：:](.+)/
    ]

    for (let pattern of namePatterns) {
      const match = message.match(pattern)
      if (match) {
        return match[1].trim()
      }
    }

    return ''
  },

  /**
   * 合并简历数据
   */
  mergeResumeData(currentData, newData) {
    if (!newData) return currentData

    return {
      ...currentData,
      ...newData,
      // 数组字段需要特殊处理
      education: newData.education ? [...currentData.education, ...newData.education] : currentData.education,
      workExperience: newData.workExperience ? [...currentData.workExperience, ...newData.workExperience] : currentData.workExperience,
      projects: newData.projects ? [...currentData.projects, ...newData.projects] : currentData.projects,
      skills: newData.skills ? [...new Set([...currentData.skills, ...newData.skills])] : currentData.skills,
      personalTraits: newData.personalTraits ? [...new Set([...currentData.personalTraits, ...newData.personalTraits])] : currentData.personalTraits
    }
  },

  /**
   * 计算简历完整度
   */
  calculateCompleteness(resumeData) {
    let score = 0
    const weights = {
      personalInfo: 20,
      jobIntention: 15,
      education: 20,
      workExperience: 25,
      projects: 15,
      skills: 5
    }
    
    // 个人信息完整度
    if (resumeData.personalInfo.name) score += weights.personalInfo * 0.4
    if (resumeData.personalInfo.phone) score += weights.personalInfo * 0.3
    if (resumeData.personalInfo.email) score += weights.personalInfo * 0.3
    
    // 求职意向完整度
    if (resumeData.jobIntention.position) score += weights.jobIntention * 0.6
    if (resumeData.jobIntention.industry) score += weights.jobIntention * 0.4
    
    // 教育背景完整度
    if (resumeData.education.length > 0) score += weights.education
    
    // 工作经历完整度
    if (resumeData.workExperience.length > 0) score += weights.workExperience
    
    // 项目经验完整度
    if (resumeData.projects.length > 0) score += weights.projects
    
    // 技能完整度
    if (resumeData.skills.length > 0) score += weights.skills
    
    return Math.round(score)
  },

  /**
   * 处理错误
   */
  handleError(errorMessage) {
    this.setData({
      isTyping: false
    })
    
    const errorMsg = {
      id: Date.now(),
      content: `抱歉，${errorMessage || '出现了一些问题'}。让我们重新开始这个话题吧～`,
      isAI: true,
      timestamp: Date.now()
    }
    
    this.setData({
      messages: [...this.data.messages, errorMsg]
    })
    
    this.scrollToBottom()
  },

  /**
   * 滚动到底部
   */
  scrollToBottom() {
    wx.createSelectorQuery().select('#messages-container').boundingClientRect((rect) => {
      if (rect) {
        wx.pageScrollTo({
          scrollTop: rect.bottom,
          duration: 300
        })
      }
    }).exec()
  },

  /**
   * 输入框输入事件
   */
  onInput(e) {
    this.setData({
      inputValue: e.detail.value
    })
  },

  /**
   * 发送按钮点击
   */
  onSend() {
    this.sendMessage()
  },

  /**
   * 快捷回复点击
   */
  onQuickReply(e) {
    const { reply } = e.currentTarget.dataset
    this.sendMessage(reply)
  },

  /**
   * 预览简历
   */
  onPreviewResume() {
    wx.navigateTo({
      url: '/pages/preview/preview'
    })
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    wx.stopPullDownRefresh()
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {
    return {
      title: '简历故事 - AI帮你写出精彩简历',
      path: '/pages/index/index'
    }
  }
})
