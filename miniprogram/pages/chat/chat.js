// pages/chat/chat.js
Page({
  /**
   * 页面的初始数据
   */
  data: {
    // 对话相关数据
    messages: [],
    inputValue: '',
    isTyping: false,
    currentStage: 'greeting',
    conversationId: null,
    
    // 简历数据
    resumeData: {
      personalInfo: {},
      jobIntention: {},
      education: [],
      workExperience: [],
      projects: [],
      skills: [],
      personalTraits: [],
      completeness: 0
    },
    
    // UI状态
    showQuickReplies: false,
    quickReplies: [],
    progress: 0,
    
    // 对话阶段配置
    conversationStages: {
      greeting: {
        title: '初次见面',
        description: '建立信任，了解用户背景',
        progress: 10
      },
      intention: {
        title: '求职意向',
        description: '了解求职目标和期望',
        progress: 20
      },
      basicInfo: {
        title: '基本信息',
        description: '收集个人基本信息',
        progress: 30
      },
      education: {
        title: '教育背景',
        description: '了解教育经历和成就',
        progress: 50
      },
      experience: {
        title: '工作经历',
        description: '挖掘工作经验和亮点',
        progress: 70
      },
      projects: {
        title: '项目经验',
        description: '探索项目经历和技能',
        progress: 85
      },
      completion: {
        title: '完成总结',
        description: '整理信息，生成简历',
        progress: 100
      }
    }
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.initializeChat()
  },

  /**
   * 初始化对话
   */
  initializeChat() {
    const welcomeMessage = {
      id: Date.now(),
      content: '嗨！我是小简，你的专属简历顾问 ✨\n\n不用担心不知道怎么写简历，我们就像朋友聊天一样。我会帮你发现自己的亮点，讲出属于你的职业故事。\n\n准备好了吗？先从你最近在找什么样的工作开始聊吧～',
      isAI: true,
      timestamp: Date.now()
    }
    
    this.setData({
      messages: [welcomeMessage],
      quickReplies: ['我是应届毕业生', '我想要跳槽', '我正在转行'],
      showQuickReplies: true,
      progress: this.data.conversationStages.greeting.progress
    })
    
    this.createConversation()
  },

  /**
   * 创建对话记录
   */
  async createConversation() {
    try {
      const result = await wx.cloud.callFunction({
        name: 'conversationManage',
        data: {
          action: 'create',
          stage: this.data.currentStage
        }
      })
      
      if (result.result.success) {
        this.setData({
          conversationId: result.result.conversationId
        })
      }
    } catch (error) {
      console.error('创建对话失败:', error)
      wx.showToast({
        title: '初始化失败，请重试',
        icon: 'none'
      })
    }
  },

  /**
   * 发送消息
   */
  async sendMessage(content) {
    if (!content && !this.data.inputValue.trim()) {
      return
    }
    
    const messageContent = content || this.data.inputValue.trim()
    
    // 添加用户消息
    const userMessage = {
      id: Date.now(),
      content: messageContent,
      isAI: false,
      timestamp: Date.now()
    }
    
    this.setData({
      messages: [...this.data.messages, userMessage],
      inputValue: '',
      isTyping: true,
      showQuickReplies: false
    })
    
    // 滚动到底部
    this.scrollToBottom()
    
    try {
      // 调用AI处理函数
      const result = await wx.cloud.callFunction({
        name: 'aiChat',
        data: {
          message: messageContent,
          conversationId: this.data.conversationId,
          currentStage: this.data.currentStage,
          resumeData: this.data.resumeData
        }
      })
      
      if (result.result.success) {
        const { aiResponse, extractedData, nextStage, suggestions } = result.result
        
        // 模拟AI思考时间
        setTimeout(() => {
          const aiMessage = {
            id: Date.now(),
            content: aiResponse,
            isAI: true,
            timestamp: Date.now()
          }
          
          // 更新简历数据
          const updatedResumeData = this.mergeResumeData(this.data.resumeData, extractedData)
          const completeness = this.calculateCompleteness(updatedResumeData)
          
          this.setData({
            messages: [...this.data.messages, aiMessage],
            isTyping: false,
            currentStage: nextStage,
            resumeData: updatedResumeData,
            quickReplies: suggestions || [],
            showQuickReplies: suggestions && suggestions.length > 0,
            progress: this.data.conversationStages[nextStage]?.progress || this.data.progress,
            'resumeData.completeness': completeness
          })
          
          this.scrollToBottom()
        }, 1000)
      } else {
        this.handleError(result.result.error)
      }
      
    } catch (error) {
      console.error('发送消息失败:', error)
      this.handleError('网络连接失败，请检查网络后重试')
    }
  },

  /**
   * 合并简历数据
   */
  mergeResumeData(currentData, newData) {
    if (!newData) return currentData
    
    return {
      ...currentData,
      ...newData,
      // 数组字段需要特殊处理
      education: newData.education ? [...currentData.education, ...newData.education] : currentData.education,
      workExperience: newData.workExperience ? [...currentData.workExperience, ...newData.workExperience] : currentData.workExperience,
      projects: newData.projects ? [...currentData.projects, ...newData.projects] : currentData.projects,
      skills: newData.skills ? [...new Set([...currentData.skills, ...newData.skills])] : currentData.skills,
      personalTraits: newData.personalTraits ? [...new Set([...currentData.personalTraits, ...newData.personalTraits])] : currentData.personalTraits
    }
  },

  /**
   * 计算简历完整度
   */
  calculateCompleteness(resumeData) {
    let score = 0
    const weights = {
      personalInfo: 20,
      jobIntention: 15,
      education: 20,
      workExperience: 25,
      projects: 15,
      skills: 5
    }
    
    // 个人信息完整度
    if (resumeData.personalInfo.name) score += weights.personalInfo * 0.4
    if (resumeData.personalInfo.phone) score += weights.personalInfo * 0.3
    if (resumeData.personalInfo.email) score += weights.personalInfo * 0.3
    
    // 求职意向完整度
    if (resumeData.jobIntention.position) score += weights.jobIntention * 0.6
    if (resumeData.jobIntention.industry) score += weights.jobIntention * 0.4
    
    // 教育背景完整度
    if (resumeData.education.length > 0) score += weights.education
    
    // 工作经历完整度
    if (resumeData.workExperience.length > 0) score += weights.workExperience
    
    // 项目经验完整度
    if (resumeData.projects.length > 0) score += weights.projects
    
    // 技能完整度
    if (resumeData.skills.length > 0) score += weights.skills
    
    return Math.round(score)
  },

  /**
   * 处理错误
   */
  handleError(errorMessage) {
    this.setData({
      isTyping: false
    })
    
    const errorMsg = {
      id: Date.now(),
      content: `抱歉，${errorMessage || '出现了一些问题'}。让我们重新开始这个话题吧～`,
      isAI: true,
      timestamp: Date.now()
    }
    
    this.setData({
      messages: [...this.data.messages, errorMsg]
    })
    
    this.scrollToBottom()
  },

  /**
   * 滚动到底部
   */
  scrollToBottom() {
    wx.createSelectorQuery().select('#messages-container').boundingClientRect((rect) => {
      if (rect) {
        wx.pageScrollTo({
          scrollTop: rect.bottom,
          duration: 300
        })
      }
    }).exec()
  },

  /**
   * 输入框输入事件
   */
  onInput(e) {
    this.setData({
      inputValue: e.detail.value
    })
  },

  /**
   * 发送按钮点击
   */
  onSend() {
    this.sendMessage()
  },

  /**
   * 快捷回复点击
   */
  onQuickReply(e) {
    const { reply } = e.currentTarget.dataset
    this.sendMessage(reply)
  },

  /**
   * 预览简历
   */
  onPreviewResume() {
    wx.navigateTo({
      url: '/pages/preview/preview'
    })
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    wx.stopPullDownRefresh()
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {
    return {
      title: '简历故事 - AI帮你写出精彩简历',
      path: '/pages/index/index'
    }
  }
})
