// pages/preview/preview.js
Page({
  /**
   * 页面的初始数据
   */
  data: {
    // 简历数据
    resumeData: {
      personalInfo: {
        name: '',
        phone: '',
        email: '',
        location: ''
      },
      jobIntention: {
        position: '',
        industry: '',
        salary: '',
        location: ''
      },
      education: [],
      workExperience: [],
      projects: [],
      skills: [],
      personalTraits: [],
      completeness: 0
    },
    
    // UI状态
    loading: true,
    currentTab: 'preview', // preview, optimize, export
    
    // 模板相关
    currentTemplate: 'modern',
    templates: [
      {
        id: 'modern',
        name: '现代简约',
        preview: '/imgs/template-modern.png',
        description: '简洁现代，适合互联网行业'
      },
      {
        id: 'classic',
        name: '经典商务',
        preview: '/imgs/template-classic.png',
        description: '传统正式，适合传统行业'
      },
      {
        id: 'creative',
        name: '创意设计',
        preview: '/imgs/template-creative.png',
        description: '富有创意，适合设计行业'
      }
    ],
    
    // 优化建议
    suggestions: [],
    optimizationScore: 0
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.loadResumeData()
  },

  /**
   * 加载简历数据
   */
  async loadResumeData() {
    try {
      // 从全局数据或云端获取简历数据
      const app = getApp()
      if (app.globalData.resumeData) {
        this.setData({
          resumeData: app.globalData.resumeData,
          loading: false
        })
      } else {
        // 从云端获取
        const result = await wx.cloud.callFunction({
          name: 'resumeManage',
          data: {
            action: 'get'
          }
        })
        
        if (result.result.success) {
          this.setData({
            resumeData: result.result.data,
            loading: false
          })
        }
      }
      
      // 生成优化建议
      this.generateSuggestions()
      
    } catch (error) {
      console.error('加载简历数据失败:', error)
      wx.showToast({
        title: '加载失败，请重试',
        icon: 'none'
      })
      this.setData({
        loading: false
      })
    }
  },

  /**
   * 生成优化建议
   */
  async generateSuggestions() {
    try {
      const result = await wx.cloud.callFunction({
        name: 'resumeAnalyze',
        data: {
          resumeData: this.data.resumeData
        }
      })
      
      if (result.result.success) {
        this.setData({
          suggestions: result.result.suggestions,
          optimizationScore: result.result.score
        })
      }
    } catch (error) {
      console.error('生成优化建议失败:', error)
    }
  },

  /**
   * 切换标签页
   */
  onTabChange(e) {
    const { tab } = e.currentTarget.dataset
    this.setData({
      currentTab: tab
    })
  },

  /**
   * 选择模板
   */
  onTemplateSelect(e) {
    const { templateId } = e.currentTarget.dataset
    this.setData({
      currentTemplate: templateId
    })
  },

  /**
   * 导出简历
   */
  async onExportResume() {
    wx.showLoading({
      title: '生成中...'
    })
    
    try {
      const result = await wx.cloud.callFunction({
        name: 'resumeGenerate',
        data: {
          resumeData: this.data.resumeData,
          templateId: this.data.currentTemplate
        }
      })
      
      wx.hideLoading()
      
      if (result.result.success) {
        // 显示导出成功，提供下载链接
        wx.showModal({
          title: '简历生成成功',
          content: '简历已生成，是否立即下载？',
          success: (res) => {
            if (res.confirm) {
              this.downloadResume(result.result.downloadUrl)
            }
          }
        })
      } else {
        wx.showToast({
          title: result.result.error || '生成失败',
          icon: 'none'
        })
      }
    } catch (error) {
      wx.hideLoading()
      console.error('导出简历失败:', error)
      wx.showToast({
        title: '导出失败，请重试',
        icon: 'none'
      })
    }
  },

  /**
   * 下载简历
   */
  downloadResume(url) {
    wx.downloadFile({
      url: url,
      success: (res) => {
        if (res.statusCode === 200) {
          wx.openDocument({
            filePath: res.tempFilePath,
            success: () => {
              wx.showToast({
                title: '下载成功',
                icon: 'success'
              })
            }
          })
        }
      },
      fail: () => {
        wx.showToast({
          title: '下载失败',
          icon: 'none'
        })
      }
    })
  },

  /**
   * 继续编辑
   */
  onContinueEdit() {
    wx.navigateBack()
  },

  /**
   * 职位优化
   */
  onJobOptimize() {
    wx.navigateTo({
      url: '/pages/optimize/optimize'
    })
  },

  /**
   * 分享简历
   */
  onShareResume() {
    wx.showActionSheet({
      itemList: ['分享给朋友', '保存到相册', '复制链接'],
      success: (res) => {
        switch (res.tapIndex) {
          case 0:
            this.shareToFriend()
            break
          case 1:
            this.saveToAlbum()
            break
          case 2:
            this.copyLink()
            break
        }
      }
    })
  },

  /**
   * 分享给朋友
   */
  shareToFriend() {
    // 实现分享逻辑
    wx.showToast({
      title: '分享功能开发中',
      icon: 'none'
    })
  },

  /**
   * 保存到相册
   */
  saveToAlbum() {
    // 实现保存到相册逻辑
    wx.showToast({
      title: '保存功能开发中',
      icon: 'none'
    })
  },

  /**
   * 复制链接
   */
  copyLink() {
    wx.setClipboardData({
      data: 'https://resume-story.example.com/share/xxx',
      success: () => {
        wx.showToast({
          title: '链接已复制',
          icon: 'success'
        })
      }
    })
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    this.loadResumeData().then(() => {
      wx.stopPullDownRefresh()
    })
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {
    return {
      title: '我的简历 - 简历故事',
      path: '/pages/index/index'
    }
  }
})
