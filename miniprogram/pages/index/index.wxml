<!--pages/index/index.wxml-->
<view class="container">
  <!-- 头部区域 -->
  <view class="header">
    <view class="hero-section">
      <view class="hero-content">
        <view class="logo">
          <text class="logo-icon">📝</text>
          <text class="logo-text">简历故事</text>
        </view>
        <view class="hero-title">
          用AI讲述你的职业故事
        </view>
        <view class="hero-subtitle">
          不再为写简历发愁，AI助手陪你聊天，自动生成专业简历
        </view>

        <!-- 统计数据 -->
        <view class="stats">
          <view class="stat-item">
            <text class="stat-number">{{stats.users}}</text>
            <text class="stat-label">用户信赖</text>
          </view>
          <view class="stat-item">
            <text class="stat-number">{{stats.resumes}}</text>
            <text class="stat-label">简历生成</text>
          </view>
          <view class="stat-item">
            <text class="stat-number">{{stats.success}}</text>
            <text class="stat-label">成功率</text>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 功能特色 -->
  <view class="features-section">
    <view class="section-title">为什么选择简历故事？</view>
    <view class="features-grid">
      <view
        class="feature-card"
        wx:for="{{features}}"
        wx:key="title"
      >
        <view class="feature-icon">{{item.icon}}</view>
        <view class="feature-title">{{item.title}}</view>
        <view class="feature-desc">{{item.desc}}</view>
      </view>
    </view>
  </view>

  <!-- 使用流程 -->
  <view class="process-section">
    <view class="section-title">简单三步，完成简历</view>
    <view class="process-steps">
      <view class="step-item">
        <view class="step-number">1</view>
        <view class="step-content">
          <view class="step-title">开始对话</view>
          <view class="step-desc">与AI助手聊天，分享你的经历</view>
        </view>
      </view>
      <view class="step-item">
        <view class="step-number">2</view>
        <view class="step-content">
          <view class="step-title">智能整理</view>
          <view class="step-desc">AI自动提取亮点，结构化信息</view>
        </view>
      </view>
      <view class="step-item">
        <view class="step-number">3</view>
        <view class="step-content">
          <view class="step-title">生成简历</view>
          <view class="step-desc">选择模板，一键生成精美简历</view>
        </view>
      </view>
    </view>
  </view>

  <!-- 底部操作区域 -->
  <view class="action-section">
    <view wx:if="{{!hasUserInfo}}" class="auth-area">
      <view class="auth-title">开始制作你的简历故事</view>
      <view class="auth-desc">需要获取基本信息来完善简历</view>
      <button
        class="auth-btn"
        bindtap="getUserProfile"
        type="primary"
      >
        授权并开始制作
      </button>
    </view>

    <view wx:else class="user-area">
      <view class="welcome-text">
        欢迎回来，{{userInfo.nickName}}！
      </view>
      <view class="action-buttons">
        <button class="primary-btn" bindtap="startResumeChat">
          开始制作简历
        </button>
        <button class="secondary-btn" bindtap="viewSampleResume">
          查看示例
        </button>
      </view>
    </view>
  </view>

  <!-- 帮助区域 -->
  <view class="help-section">
    <view class="help-item" bindtap="viewHelp">
      <text class="help-icon">❓</text>
      <text class="help-text">如何使用</text>
    </view>
    <view class="help-item" bindtap="viewSampleResume">
      <text class="help-icon">👀</text>
      <text class="help-text">查看示例</text>
    </view>
  </view>
</view>

