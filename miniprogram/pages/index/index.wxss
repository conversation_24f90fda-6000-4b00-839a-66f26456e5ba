/* pages/index/index.wxss */

/* 页面容器 */
.container {
  min-height: 100vh;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
}

/* 头部区域 */
.header {
  background: linear-gradient(135deg, #0ea5e9 0%, #3b82f6 100%);
  color: #ffffff;
  padding: 80rpx 32rpx 60rpx;
}

.hero-section {
  text-align: center;
}

.logo {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 32rpx;
}

.logo-icon {
  font-size: 64rpx;
  margin-right: 16rpx;
}

.logo-text {
  font-size: 48rpx;
  font-weight: 700;
}

.hero-title {
  font-size: 56rpx;
  font-weight: 700;
  margin-bottom: 24rpx;
  line-height: 1.3;
}

.hero-subtitle {
  font-size: 28rpx;
  opacity: 0.9;
  line-height: 1.5;
  margin-bottom: 48rpx;
  max-width: 600rpx;
  margin-left: auto;
  margin-right: auto;
}

/* 统计数据 */
.stats {
  display: flex;
  justify-content: center;
  gap: 48rpx;
  margin-top: 32rpx;
}

.stat-item {
  text-align: center;
}

.stat-number {
  display: block;
  font-size: 40rpx;
  font-weight: 700;
  margin-bottom: 8rpx;
}

.stat-label {
  font-size: 24rpx;
  opacity: 0.8;
}

/* 功能特色区域 */
.features-section {
  padding: 80rpx 32rpx;
  background: #ffffff;
}

.section-title {
  font-size: 48rpx;
  font-weight: 700;
  text-align: center;
  color: #1e293b;
  margin-bottom: 64rpx;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 32rpx;
  max-width: 800rpx;
  margin: 0 auto;
}

.feature-card {
  background: #f8fafc;
  border-radius: 24rpx;
  padding: 48rpx 32rpx;
  text-align: center;
  transition: all 0.3s ease;
}

.feature-card:active {
  transform: scale(0.98);
  background: #f1f5f9;
}

.feature-icon {
  font-size: 64rpx;
  margin-bottom: 24rpx;
}

.feature-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 16rpx;
}

.feature-desc {
  font-size: 24rpx;
  color: #64748b;
  line-height: 1.5;
}

/* 使用流程区域 */
.process-section {
  padding: 80rpx 32rpx;
  background: #f8fafc;
}

.process-steps {
  max-width: 800rpx;
  margin: 0 auto;
}

.step-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 48rpx;
}

.step-item:last-child {
  margin-bottom: 0;
}

.step-number {
  width: 80rpx;
  height: 80rpx;
  background: linear-gradient(135deg, #0ea5e9, #3b82f6);
  color: #ffffff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  font-weight: 700;
  margin-right: 32rpx;
  flex-shrink: 0;
}

.step-content {
  flex: 1;
  padding-top: 8rpx;
}

.step-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 12rpx;
}

.step-desc {
  font-size: 24rpx;
  color: #64748b;
  line-height: 1.5;
}

/* 操作区域 */
.action-section {
  padding: 80rpx 32rpx;
  background: #ffffff;
  text-align: center;
}

.auth-area {
  max-width: 600rpx;
  margin: 0 auto;
}

.auth-title {
  font-size: 40rpx;
  font-weight: 700;
  color: #1e293b;
  margin-bottom: 16rpx;
}

.auth-desc {
  font-size: 24rpx;
  color: #64748b;
  margin-bottom: 48rpx;
  line-height: 1.5;
}

.auth-btn {
  background: linear-gradient(135deg, #0ea5e9, #3b82f6);
  border-radius: 32rpx;
  font-size: 28rpx;
  font-weight: 600;
}

.user-area {
  max-width: 600rpx;
  margin: 0 auto;
}

.welcome-text {
  font-size: 32rpx;
  color: #1e293b;
  margin-bottom: 48rpx;
}

.action-buttons {
  display: flex;
  gap: 24rpx;
  justify-content: center;
}

.primary-btn {
  flex: 1;
  background: linear-gradient(135deg, #0ea5e9, #3b82f6);
  color: #ffffff;
  border-radius: 32rpx;
  font-size: 28rpx;
  font-weight: 600;
  padding: 24rpx;
}

.secondary-btn {
  flex: 1;
  background: #f8fafc;
  color: #475569;
  border: 1px solid #e2e8f0;
  border-radius: 32rpx;
  font-size: 28rpx;
  font-weight: 600;
  padding: 24rpx;
}

/* 帮助区域 */
.help-section {
  padding: 48rpx 32rpx;
  background: #f8fafc;
  display: flex;
  justify-content: center;
  gap: 48rpx;
}

.help-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 24rpx;
  background: #ffffff;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
}

.help-item:active {
  transform: scale(0.95);
}

.help-icon {
  font-size: 48rpx;
  margin-bottom: 12rpx;
}

.help-text {
  font-size: 24rpx;
  color: #475569;
}

/* 响应式适配 */
@media (max-width: 750rpx) {
  .features-grid {
    grid-template-columns: 1fr;
    gap: 24rpx;
  }

  .stats {
    gap: 32rpx;
  }

  .action-buttons {
    flex-direction: column;
  }

  .help-section {
    gap: 24rpx;
  }
}